#!/usr/bin/env python3
"""
🧪 简化优化测试
测试基本的优化参数是否能正常工作
"""

import subprocess
import json
import re
import sys
import os
from datetime import datetime

def run_simple_test():
    """运行简化的优化测试"""
    
    print("🧪 === 简化优化测试 ===")
    print("🎯 测试基本优化参数是否正常工作")
    
    cmd = [
        "python", "src/train.py", 
        "ConstPos", "simple_optimization_test",
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", "15",  # 减少训练轮数
        "--batch-size", "16",
        "--lr", "0.001",  # 使用标准学习率
        "--gpu", "0",
        "--reconstruct_target", "original",
        # 🎯 测试优化参数
        "--lambda_decl", "0.3",  # 降低对比损失权重
        "--temp_contrast", "0.5",  # 降低对比温度
        "--lambda_align", "0.05"  # 降低方向对齐权重
    ]
    
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=900)  # 15分钟超时
        
        if result.returncode != 0:
            print(f"❌ 测试失败:")
            print(f"   stderr: {result.stderr}")
            print(f"   stdout: {result.stdout}")
            return False
        
        output = result.stdout
        print(f"✅ 测试完成，解析结果...")
        
        # 解析性能指标
        f1_match = re.search(r"f1[:\s]+([0-9.]+)", output, re.IGNORECASE)
        precision_match = re.search(r"precision[:\s]+([0-9.]+)", output, re.IGNORECASE)
        recall_match = re.search(r"recall[:\s]+([0-9.]+)", output, re.IGNORECASE)
        
        f1_score = float(f1_match.group(1)) if f1_match else 0.0
        precision = float(precision_match.group(1)) if precision_match else 0.0
        recall = float(recall_match.group(1)) if recall_match else 0.0
        
        print(f"📊 测试结果:")
        print(f"   F1分数: {f1_score:.4f}")
        print(f"   精度: {precision:.4f}")
        print(f"   召回率: {recall:.4f}")
        
        # 检查是否有改进
        if precision > 0.45:  # 比原来的41.6%有改进
            print(f"   ✅ 精度有改进! ({precision:.4f} > 0.416)")
        else:
            print(f"   ⚠️ 精度仍需改进 ({precision:.4f} <= 0.45)")
            
        if f1_score > 0.58:  # 比原来的0.56有改进
            print(f"   ✅ F1分数有改进! ({f1_score:.4f} > 0.56)")
        else:
            print(f"   ⚠️ F1分数仍需改进 ({f1_score:.4f} <= 0.58)")
        
        # 检查训练稳定性
        nan_pattern = r"(nan|NaN|inf|Inf)"
        training_stable = not bool(re.search(nan_pattern, output))
        print(f"   训练稳定性: {'✅ 稳定' if training_stable else '❌ 不稳定'}")
        
        # 保存详细输出用于调试
        with open("simple_test_output.txt", "w", encoding="utf-8") as f:
            f.write(output)
        print(f"   📝 详细输出已保存到: simple_test_output.txt")
        
        return True
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 (15分钟)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始简化优化测试...")
    
    success = run_simple_test()
    
    if success:
        print(f"\n✅ 基本测试通过!")
        print(f"📋 下一步:")
        print(f"   1. 检查 simple_test_output.txt 了解详细训练过程")
        print(f"   2. 如果性能有改进，可以运行完整的优化实验")
        print(f"   3. 如果性能没有改进，需要进一步调试优化代码")
    else:
        print(f"\n❌ 基本测试失败!")
        print(f"📋 需要检查:")
        print(f"   1. 代码是否有语法错误")
        print(f"   2. 新增的优化组件是否正确集成")
        print(f"   3. 参数传递是否正确")

if __name__ == "__main__":
    main()
