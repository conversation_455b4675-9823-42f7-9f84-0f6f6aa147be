Dataset: ConstPos
Arguments: Namespace(batch_size=16, dataset='ConstPos', epochs=15, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.1, lambda_decl=0.1, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.001, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, patience=10, precision_target=0.65, recall_target=0.75, reconstruct_target='original', repr_dims=320, run_name='quick_baseline_test', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=1.0, use_focal_loss=False)
✅ 已设置随机种子: 42
Loading data... done
(480, 50, 37)

🔍 === TRAINING FLOW INVESTIGATION ===
📊 Input train_data type: <class 'dict'>
📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])
🎯 DECL Mode Detected: True
🔄 === DECL TRAINING BRANCH ===
📈 Original data shape: (480, 50, 37)
📈 Denoised data shape: (480, 50, 37)
📈 Noisy data shape: (480, 50, 37)
📈 Mask shape: (480, 50, 36)
📈 Batch size: 16
🎯 初始化Gumbel-Softmax门控网络...
🚀 初始化基于Gumbel-Softmax的自适应去噪系统...
✅ 自适应去噪模块已创建
   - 输入维度: 36
   - 去噪器数量: 7
   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']
✅ 门控网络初始化完成
🌡️ 温度退火设置: 初始2.0 -> 最终0.1
📊 DataLoader created with 30 batches
📊 Expected batches per epoch: 30
🔧 DECL Optimizer created with 64 parameters (包含门控网络)

🎯 === TRAINING PARAMETERS INVESTIGATION ===
📊 Input n_epochs: 15
📊 Input n_iters: None
📊 Final training parameters:
   - n_epochs: 15
   - n_iters: None
   - max_epochs: 15
   - batches_per_epoch: 30
   - self.n_epochs: 0
   - self.n_iters: 0

🚀 === STARTING TRAINING LOOP ===

📊 === 损失函数配置 ===
   🔧 重构损失权重 (w_rec): 1.0
   🔧 三元组损失权重 (w_triplet): 0.1
   ✅ 分离损失权重 (w_sep): 已完全移除
   📝 总损失公式: Total = 1.0 * Reconstruction + 0.1 * Triplet
   🌡️ 对比温度 (temp_contrast): 1.0
   🎯 方向对齐权重 (lambda_align): 0.1

📅 Starting Epoch 0
🔍 Check termination: n_epochs=15, self.n_epochs=0
🔄 === DECL TRAINING LOOP (Epoch 0) ===
   🔍 === 损失组件验证 ===
      ✅ 重构损失: 启用 (权重=1.0)
      ✅ 三元组损失: 启用 (权重=0.1)
      ❌ 分离损失: 已移除 (不存在w_sep属性)
      📝 预期损失计算: loss = 1.0 * loss_rec + 0.1 * loss_triplet
      ✅ 确认: w_sep属性已完全移除
   📊 Batch 5: Total=1.3155 (Rec=1.2593, Trip=0.5620)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.986102
      🔧 三元组损失 (Triplet): 0.530382
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.986102 = 0.986102
      📝 加权三元组损失: 0.100 × 0.530382 = 0.053038
      🎯 总损失 (Total): 1.039140
      ✅ 验证公式: 1.039140 = 1.039140
   📊 Batch 15: Total=0.7659 (Rec=0.7192, Trip=0.4667)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.864101
      🔧 三元组损失 (Triplet): 0.467022
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.864101 = 1.864101
      📝 加权三元组损失: 0.100 × 0.467022 = 0.046702
      🎯 总损失 (Total): 1.910803
      ✅ 验证公式: 1.910803 = 1.910803
   📊 Batch 25: Total=0.9062 (Rec=0.8605, Trip=0.4569)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.844677
      🔧 三元组损失 (Triplet): 0.434741
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.844677 = 0.844677
      📝 加权三元组损失: 0.100 × 0.434741 = 0.043474
      🎯 总损失 (Total): 0.888151
      ✅ 验证公式: 0.888151 = 0.888151
📊 DECL Epoch 0 completed: 30 batches processed

📊 === EPOCH 0 COMPLETED ===
   ⏱️ Epoch time: 3.152s
   ⏱️ Total time so far: 3.152s
   📈 Batches processed: 30
   📈 Average total loss: 1.004910
   📈 Total iterations so far: 30
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #0: loss=1.0049102028210959
   📈 Incremented self.n_epochs to: 1

📅 Starting Epoch 1
🔍 Check termination: n_epochs=15, self.n_epochs=1
🔄 === DECL TRAINING LOOP (Epoch 1) ===
   📊 Batch 5: Total=0.8011 (Rec=0.7581, Trip=0.4301)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 1.048779
      🔧 三元组损失 (Triplet): 0.401281
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.048779 = 1.048779
      📝 加权三元组损失: 0.100 × 0.401281 = 0.040128
      🎯 总损失 (Total): 1.088907
      ✅ 验证公式: 1.088907 = 1.088907
   📊 Batch 15: Total=1.0335 (Rec=0.9960, Trip=0.3747)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.710319
      🔧 三元组损失 (Triplet): 0.324815
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.710319 = 0.710319
      📝 加权三元组损失: 0.100 × 0.324815 = 0.032482
      🎯 总损失 (Total): 0.742800
      ✅ 验证公式: 0.742800 = 0.742800
   📊 Batch 25: Total=0.9522 (Rec=0.9220, Trip=0.3015)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.001493
      🔧 三元组损失 (Triplet): 0.249920
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.001493 = 1.001493
      📝 加权三元组损失: 0.100 × 0.249920 = 0.024992
      🎯 总损失 (Total): 1.026485
      ✅ 验证公式: 1.026485 = 1.026485
📊 DECL Epoch 1 completed: 30 batches processed

📊 === EPOCH 1 COMPLETED ===
   ⏱️ Epoch time: 2.689s
   ⏱️ Total time so far: 5.840s
   📈 Batches processed: 30
   📈 Average total loss: 0.917402
   📈 Total iterations so far: 60
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #1: loss=0.9174022018909455
   📈 Incremented self.n_epochs to: 2

📅 Starting Epoch 2
🔍 Check termination: n_epochs=15, self.n_epochs=2
🔄 === DECL TRAINING LOOP (Epoch 2) ===
   📊 Batch 5: Total=0.8270 (Rec=0.8058, Trip=0.2119)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.814349
      🔧 三元组损失 (Triplet): 0.199334
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.814349 = 0.814349
      📝 加权三元组损失: 0.100 × 0.199334 = 0.019933
      🎯 总损失 (Total): 0.834283
      ✅ 验证公式: 0.834283 = 0.834283
   📊 Batch 15: Total=0.7523 (Rec=0.7334, Trip=0.1886)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.171365
      🔧 三元组损失 (Triplet): 0.199768
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.171365 = 1.171365
      📝 加权三元组损失: 0.100 × 0.199768 = 0.019977
      🎯 总损失 (Total): 1.191342
      ✅ 验证公式: 1.191342 = 1.191342
   📊 Batch 25: Total=0.7385 (Rec=0.7180, Trip=0.2047)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.887442
      🔧 三元组损失 (Triplet): 0.185247
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.887442 = 0.887442
      📝 加权三元组损失: 0.100 × 0.185247 = 0.018525
      🎯 总损失 (Total): 0.905967
      ✅ 验证公式: 0.905967 = 0.905967
📊 DECL Epoch 2 completed: 30 batches processed

📊 === EPOCH 2 COMPLETED ===
   ⏱️ Epoch time: 2.609s
   ⏱️ Total time so far: 8.449s
   📈 Batches processed: 30
   📈 Average total loss: 0.871354
   📈 Total iterations so far: 90
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #2: loss=0.8713541626930237
   📈 Incremented self.n_epochs to: 3

📅 Starting Epoch 3
🔍 Check termination: n_epochs=15, self.n_epochs=3
🔄 === DECL TRAINING LOOP (Epoch 3) ===
   📊 Batch 5: Total=0.6797 (Rec=0.6607, Trip=0.1900)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.730868
      🔧 三元组损失 (Triplet): 0.181674
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.730868 = 0.730868
      📝 加权三元组损失: 0.100 × 0.181674 = 0.018167
      🎯 总损失 (Total): 0.749035
      ✅ 验证公式: 0.749035 = 0.749035
   📊 Batch 15: Total=0.8033 (Rec=0.7857, Trip=0.1757)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.781275
      🔧 三元组损失 (Triplet): 0.178989
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.781275 = 0.781275
      📝 加权三元组损失: 0.100 × 0.178989 = 0.017899
      🎯 总损失 (Total): 0.799174
      ✅ 验证公式: 0.799174 = 0.799174
   📊 Batch 25: Total=0.8848 (Rec=0.8669, Trip=0.1785)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.065070
      🔧 三元组损失 (Triplet): 0.176344
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.065070 = 1.065070
      📝 加权三元组损失: 0.100 × 0.176344 = 0.017634
      🎯 总损失 (Total): 1.082704
      ✅ 验证公式: 1.082704 = 1.082704
📊 DECL Epoch 3 completed: 30 batches processed

📊 === EPOCH 3 COMPLETED ===
   ⏱️ Epoch time: 2.654s
   ⏱️ Total time so far: 11.104s
   📈 Batches processed: 30
   📈 Average total loss: 0.892041
   📈 Total iterations so far: 120
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #3: loss=0.8920407513777415
   📈 Incremented self.n_epochs to: 4

📅 Starting Epoch 4
🔍 Check termination: n_epochs=15, self.n_epochs=4
🔄 === DECL TRAINING LOOP (Epoch 4) ===
   📊 Batch 5: Total=0.8536 (Rec=0.8367, Trip=0.1696)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.824931
      🔧 三元组损失 (Triplet): 0.168330
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.824931 = 0.824931
      📝 加权三元组损失: 0.100 × 0.168330 = 0.016833
      🎯 总损失 (Total): 0.841764
      ✅ 验证公式: 0.841764 = 0.841764
   📊 Batch 15: Total=0.8316 (Rec=0.8133, Trip=0.1833)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.694303
      🔧 三元组损失 (Triplet): 0.175047
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.694303 = 0.694303
      📝 加权三元组损失: 0.100 × 0.175047 = 0.017505
      🎯 总损失 (Total): 0.711808
      ✅ 验证公式: 0.711808 = 0.711808
   📊 Batch 25: Total=0.7821 (Rec=0.7645, Trip=0.1761)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.805391
      🔧 三元组损失 (Triplet): 0.183334
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.805391 = 0.805391
      📝 加权三元组损失: 0.100 × 0.183334 = 0.018333
      🎯 总损失 (Total): 0.823724
      ✅ 验证公式: 0.823724 = 0.823724
📊 DECL Epoch 4 completed: 30 batches processed

📊 === EPOCH 4 COMPLETED ===
   ⏱️ Epoch time: 2.528s
   ⏱️ Total time so far: 13.632s
   📈 Batches processed: 30
   📈 Average total loss: 0.832514
   📈 Total iterations so far: 150
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #4: loss=0.8325137396653494
   📈 Incremented self.n_epochs to: 5

📅 Starting Epoch 5
🔍 Check termination: n_epochs=15, self.n_epochs=5
🔄 === DECL TRAINING LOOP (Epoch 5) ===
   📊 Batch 5: Total=0.7983 (Rec=0.7805, Trip=0.1779)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.810063
      🔧 三元组损失 (Triplet): 0.171522
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.810063 = 0.810063
      📝 加权三元组损失: 0.100 × 0.171522 = 0.017152
      🎯 总损失 (Total): 0.827215
      ✅ 验证公式: 0.827215 = 0.827215
   📊 Batch 15: Total=0.8115 (Rec=0.7945, Trip=0.1695)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.830616
      🔧 三元组损失 (Triplet): 0.171737
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.830616 = 0.830616
      📝 加权三元组损失: 0.100 × 0.171737 = 0.017174
      🎯 总损失 (Total): 0.847790
      ✅ 验证公式: 0.847790 = 0.847790
   📊 Batch 25: Total=0.7903 (Rec=0.7732, Trip=0.1706)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.668343
      🔧 三元组损失 (Triplet): 0.164105
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.668343 = 0.668343
      📝 加权三元组损失: 0.100 × 0.164105 = 0.016410
      🎯 总损失 (Total): 0.684753
      ✅ 验证公式: 0.684753 = 0.684753
📊 DECL Epoch 5 completed: 30 batches processed

📊 === EPOCH 5 COMPLETED ===
   ⏱️ Epoch time: 2.412s
   ⏱️ Total time so far: 16.044s
   📈 Batches processed: 30
   📈 Average total loss: 0.831117
   📈 Total iterations so far: 180
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #5: loss=0.8311167240142823
   📈 Incremented self.n_epochs to: 6

📅 Starting Epoch 6
🔍 Check termination: n_epochs=15, self.n_epochs=6
🔄 === DECL TRAINING LOOP (Epoch 6) ===
   📊 Batch 5: Total=0.7443 (Rec=0.7277, Trip=0.1657)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.797614
      🔧 三元组损失 (Triplet): 0.164922
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.797614 = 0.797614
      📝 加权三元组损失: 0.100 × 0.164922 = 0.016492
      🎯 总损失 (Total): 0.814106
      ✅ 验证公式: 0.814106 = 0.814106
   📊 Batch 15: Total=0.6318 (Rec=0.6145, Trip=0.1734)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.867134
      🔧 三元组损失 (Triplet): 0.167731
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.867134 = 1.867134
      📝 加权三元组损失: 0.100 × 0.167731 = 0.016773
      🎯 总损失 (Total): 1.883907
      ✅ 验证公式: 1.883907 = 1.883907
   📊 Batch 25: Total=0.7101 (Rec=0.6935, Trip=0.1662)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.748937
      🔧 三元组损失 (Triplet): 0.167349
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.748937 = 0.748937
      📝 加权三元组损失: 0.100 × 0.167349 = 0.016735
      🎯 总损失 (Total): 0.765672
      ✅ 验证公式: 0.765672 = 0.765672
📊 DECL Epoch 6 completed: 30 batches processed

📊 === EPOCH 6 COMPLETED ===
   ⏱️ Epoch time: 2.542s
   ⏱️ Total time so far: 18.586s
   📈 Batches processed: 30
   📈 Average total loss: 0.878781
   📈 Total iterations so far: 210
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #6: loss=0.8787814021110535
   📈 Incremented self.n_epochs to: 7

📅 Starting Epoch 7
🔍 Check termination: n_epochs=15, self.n_epochs=7
🔄 === DECL TRAINING LOOP (Epoch 7) ===
   📊 Batch 5: Total=0.8820 (Rec=0.8655, Trip=0.1645)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.815635
      🔧 三元组损失 (Triplet): 0.164576
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.815635 = 0.815635
      📝 加权三元组损失: 0.100 × 0.164576 = 0.016458
      🎯 总损失 (Total): 0.832093
      ✅ 验证公式: 0.832093 = 0.832093
   📊 Batch 15: Total=0.7589 (Rec=0.7423, Trip=0.1659)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.698696
      🔧 三元组损失 (Triplet): 0.166595
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.698696 = 0.698696
      📝 加权三元组损失: 0.100 × 0.166595 = 0.016659
      🎯 总损失 (Total): 0.715356
      ✅ 验证公式: 0.715356 = 0.715356
   📊 Batch 25: Total=0.9626 (Rec=0.9460, Trip=0.1658)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.761520
      🔧 三元组损失 (Triplet): 0.166101
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.761520 = 0.761520
      📝 加权三元组损失: 0.100 × 0.166101 = 0.016610
      🎯 总损失 (Total): 0.778130
      ✅ 验证公式: 0.778130 = 0.778130
📊 DECL Epoch 7 completed: 30 batches processed

📊 === EPOCH 7 COMPLETED ===
   ⏱️ Epoch time: 2.441s
   ⏱️ Total time so far: 21.028s
   📈 Batches processed: 30
   📈 Average total loss: 0.808513
   📈 Total iterations so far: 240
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #7: loss=0.8085128009319306
   📈 Incremented self.n_epochs to: 8

📅 Starting Epoch 8
🔍 Check termination: n_epochs=15, self.n_epochs=8
🔄 === DECL TRAINING LOOP (Epoch 8) ===
   📊 Batch 5: Total=0.6967 (Rec=0.6800, Trip=0.1660)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.608315
      🔧 三元组损失 (Triplet): 0.168815
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.608315 = 0.608315
      📝 加权三元组损失: 0.100 × 0.168815 = 0.016881
      🎯 总损失 (Total): 0.625197
      ✅ 验证公式: 0.625197 = 0.625197
   📊 Batch 15: Total=0.8258 (Rec=0.8089, Trip=0.1694)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.849774
      🔧 三元组损失 (Triplet): 0.172346
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.849774 = 0.849774
      📝 加权三元组损失: 0.100 × 0.172346 = 0.017235
      🎯 总损失 (Total): 0.867009
      ✅ 验证公式: 0.867009 = 0.867009
   📊 Batch 25: Total=0.6833 (Rec=0.6658, Trip=0.1749)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.667519
      🔧 三元组损失 (Triplet): 0.165304
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.667519 = 0.667519
      📝 加权三元组损失: 0.100 × 0.165304 = 0.016530
      🎯 总损失 (Total): 0.684050
      ✅ 验证公式: 0.684050 = 0.684050
📊 DECL Epoch 8 completed: 30 batches processed

📊 === EPOCH 8 COMPLETED ===
   ⏱️ Epoch time: 2.431s
   ⏱️ Total time so far: 23.459s
   📈 Batches processed: 30
   📈 Average total loss: 0.784970
   📈 Total iterations so far: 270
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #8: loss=0.784970368941625
   📈 Incremented self.n_epochs to: 9

📅 Starting Epoch 9
🔍 Check termination: n_epochs=15, self.n_epochs=9
🔄 === DECL TRAINING LOOP (Epoch 9) ===
   📊 Batch 5: Total=0.7469 (Rec=0.7304, Trip=0.1648)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.876902
      🔧 三元组损失 (Triplet): 0.166821
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.876902 = 0.876902
      📝 加权三元组损失: 0.100 × 0.166821 = 0.016682
      🎯 总损失 (Total): 0.893585
      ✅ 验证公式: 0.893585 = 0.893585
   📊 Batch 15: Total=0.6403 (Rec=0.6230, Trip=0.1729)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.754406
      🔧 三元组损失 (Triplet): 0.168855
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.754406 = 0.754406
      📝 加权三元组损失: 0.100 × 0.168855 = 0.016886
      🎯 总损失 (Total): 0.771291
      ✅ 验证公式: 0.771291 = 0.771291
   📊 Batch 25: Total=0.9403 (Rec=0.9234, Trip=0.1692)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.823423
      🔧 三元组损失 (Triplet): 0.169334
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.823423 = 0.823423
      📝 加权三元组损失: 0.100 × 0.169334 = 0.016933
      🎯 总损失 (Total): 0.840357
      ✅ 验证公式: 0.840357 = 0.840357
📊 DECL Epoch 9 completed: 30 batches processed

📊 === EPOCH 9 COMPLETED ===
   ⏱️ Epoch time: 4.043s
   ⏱️ Total time so far: 27.502s
   📈 Batches processed: 30
   📈 Average total loss: 0.829650
   📈 Total iterations so far: 300
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #9: loss=0.8296498159567515
   📈 Incremented self.n_epochs to: 10

📅 Starting Epoch 10
🔍 Check termination: n_epochs=15, self.n_epochs=10
🔄 === DECL TRAINING LOOP (Epoch 10) ===
   📊 Batch 5: Total=0.8588 (Rec=0.8423, Trip=0.1646)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.691671
      🔧 三元组损失 (Triplet): 0.169638
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.691671 = 0.691671
      📝 加权三元组损失: 0.100 × 0.169638 = 0.016964
      🎯 总损失 (Total): 0.708635
      ✅ 验证公式: 0.708635 = 0.708635
   📊 Batch 15: Total=0.7439 (Rec=0.7273, Trip=0.1661)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.738350
      🔧 三元组损失 (Triplet): 0.165580
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.738350 = 0.738350
      📝 加权三元组损失: 0.100 × 0.165580 = 0.016558
      🎯 总损失 (Total): 0.754908
      ✅ 验证公式: 0.754908 = 0.754908
   📊 Batch 25: Total=0.6761 (Rec=0.6596, Trip=0.1655)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.595590
      🔧 三元组损失 (Triplet): 0.163653
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.595590 = 0.595590
      📝 加权三元组损失: 0.100 × 0.163653 = 0.016365
      🎯 总损失 (Total): 0.611955
      ✅ 验证公式: 0.611955 = 0.611955
📊 DECL Epoch 10 completed: 30 batches processed

📊 === EPOCH 10 COMPLETED ===
   ⏱️ Epoch time: 3.328s
   ⏱️ Total time so far: 30.830s
   📈 Batches processed: 30
   📈 Average total loss: 0.785637
   📈 Total iterations so far: 330
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #10: loss=0.7856365164120992
   📈 Incremented self.n_epochs to: 11

📅 Starting Epoch 11
🔍 Check termination: n_epochs=15, self.n_epochs=11
🔄 === DECL TRAINING LOOP (Epoch 11) ===
   📊 Batch 5: Total=0.6096 (Rec=0.5931, Trip=0.1646)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.587325
      🔧 三元组损失 (Triplet): 0.163172
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.587325 = 0.587325
      📝 加权三元组损失: 0.100 × 0.163172 = 0.016317
      🎯 总损失 (Total): 0.603642
      ✅ 验证公式: 0.603642 = 0.603642
   📊 Batch 15: Total=0.6351 (Rec=0.6184, Trip=0.1671)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.734135
      🔧 三元组损失 (Triplet): 0.164795
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.734135 = 0.734135
      📝 加权三元组损失: 0.100 × 0.164795 = 0.016479
      🎯 总损失 (Total): 0.750615
      ✅ 验证公式: 0.750615 = 0.750615
   📊 Batch 25: Total=0.6501 (Rec=0.6334, Trip=0.1666)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.720927
      🔧 三元组损失 (Triplet): 0.167718
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.720927 = 0.720927
      📝 加权三元组损失: 0.100 × 0.167718 = 0.016772
      🎯 总损失 (Total): 0.737699
      ✅ 验证公式: 0.737699 = 0.737699
📊 DECL Epoch 11 completed: 30 batches processed

📊 === EPOCH 11 COMPLETED ===
   ⏱️ Epoch time: 2.420s
   ⏱️ Total time so far: 33.250s
   📈 Batches processed: 30
   📈 Average total loss: 0.786571
   📈 Total iterations so far: 360
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #11: loss=0.7865711450576782
   📈 Incremented self.n_epochs to: 12

📅 Starting Epoch 12
🔍 Check termination: n_epochs=15, self.n_epochs=12
🔄 === DECL TRAINING LOOP (Epoch 12) ===
   📊 Batch 5: Total=0.7551 (Rec=0.7379, Trip=0.1722)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.633119
      🔧 三元组损失 (Triplet): 0.174666
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.633119 = 0.633119
      📝 加权三元组损失: 0.100 × 0.174666 = 0.017467
      🎯 总损失 (Total): 0.650586
      ✅ 验证公式: 0.650586 = 0.650586
   📊 Batch 15: Total=0.7151 (Rec=0.6982, Trip=0.1685)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.734037
      🔧 三元组损失 (Triplet): 0.167467
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.734037 = 0.734037
      📝 加权三元组损失: 0.100 × 0.167467 = 0.016747
      🎯 总损失 (Total): 0.750783
      ✅ 验证公式: 0.750783 = 0.750783
   📊 Batch 25: Total=0.8403 (Rec=0.8239, Trip=0.1647)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.207259
      🔧 三元组损失 (Triplet): 0.166703
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.207259 = 1.207259
      📝 加权三元组损失: 0.100 × 0.166703 = 0.016670
      🎯 总损失 (Total): 1.223929
      ✅ 验证公式: 1.223929 = 1.223929
📊 DECL Epoch 12 completed: 30 batches processed

📊 === EPOCH 12 COMPLETED ===
   ⏱️ Epoch time: 2.544s
   ⏱️ Total time so far: 35.794s
   📈 Batches processed: 30
   📈 Average total loss: 0.814694
   📈 Total iterations so far: 390
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #12: loss=0.8146941244602204
   📈 Incremented self.n_epochs to: 13

📅 Starting Epoch 13
🔍 Check termination: n_epochs=15, self.n_epochs=13
🔄 === DECL TRAINING LOOP (Epoch 13) ===
   📊 Batch 5: Total=0.8121 (Rec=0.7956, Trip=0.1643)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.642277
      🔧 三元组损失 (Triplet): 0.164145
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.642277 = 0.642277
      📝 加权三元组损失: 0.100 × 0.164145 = 0.016414
      🎯 总损失 (Total): 0.658692
      ✅ 验证公式: 0.658692 = 0.658692
   📊 Batch 15: Total=1.0203 (Rec=1.0038, Trip=0.1649)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.862182
      🔧 三元组损失 (Triplet): 0.164943
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.862182 = 0.862182
      📝 加权三元组损失: 0.100 × 0.164943 = 0.016494
      🎯 总损失 (Total): 0.878676
      ✅ 验证公式: 0.878676 = 0.878676
   📊 Batch 25: Total=0.6311 (Rec=0.6150, Trip=0.1615)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.652677
      🔧 三元组损失 (Triplet): 0.163651
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.652677 = 0.652677
      📝 加权三元组损失: 0.100 × 0.163651 = 0.016365
      🎯 总损失 (Total): 0.669042
      ✅ 验证公式: 0.669042 = 0.669042
📊 DECL Epoch 13 completed: 30 batches processed

📊 === EPOCH 13 COMPLETED ===
   ⏱️ Epoch time: 2.507s
   ⏱️ Total time so far: 38.301s
   📈 Batches processed: 30
   📈 Average total loss: 0.772541
   📈 Total iterations so far: 420
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #13: loss=0.772541097799937
   📈 Incremented self.n_epochs to: 14

📅 Starting Epoch 14
🔍 Check termination: n_epochs=15, self.n_epochs=14
🔄 === DECL TRAINING LOOP (Epoch 14) ===
   📊 Batch 5: Total=0.8382 (Rec=0.8219, Trip=0.1628)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.664221
      🔧 三元组损失 (Triplet): 0.162299
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.664221 = 0.664221
      📝 加权三元组损失: 0.100 × 0.162299 = 0.016230
      🎯 总损失 (Total): 0.680451
      ✅ 验证公式: 0.680451 = 0.680451
   📊 Batch 15: Total=0.6654 (Rec=0.6487, Trip=0.1671)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.846263
      🔧 三元组损失 (Triplet): 0.164949
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.846263 = 0.846263
      📝 加权三元组损失: 0.100 × 0.164949 = 0.016495
      🎯 总损失 (Total): 0.862758
      ✅ 验证公式: 0.862758 = 0.862758
   📊 Batch 25: Total=0.6693 (Rec=0.6529, Trip=0.1634)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.737544
      🔧 三元组损失 (Triplet): 0.169676
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.737544 = 0.737544
      📝 加权三元组损失: 0.100 × 0.169676 = 0.016968
      🎯 总损失 (Total): 0.754512
      ✅ 验证公式: 0.754512 = 0.754512
📊 DECL Epoch 14 completed: 30 batches processed

📊 === EPOCH 14 COMPLETED ===
   ⏱️ Epoch time: 2.516s
   ⏱️ Total time so far: 40.817s
   📈 Batches processed: 30
   📈 Average total loss: 0.755217
   📈 Total iterations so far: 450
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #14: loss=0.7552170594533284
   📈 Incremented self.n_epochs to: 15

📅 Starting Epoch 15
🔍 Check termination: n_epochs=15, self.n_epochs=15
🛑 Breaking due to epoch limit: 15 >= 15

🏁 === TRAINING COMPLETED ===
   ⏱️ Total training time: 40.817s
   📈 Total epochs completed: 15
   📈 Total iterations completed: 450
   📈 Final loss: 0.7552170594533284
   📊 Loss history: [1.0049102028210959, 0.9174022018909455, 0.8713541626930237, 0.8920407513777415, 0.8325137396653494, 0.8311167240142823, 0.8787814021110535, 0.8085128009319306, 0.784970368941625, 0.8296498159567515, 0.7856365164120992, 0.7865711450576782, 0.8146941244602204, 0.772541097799937, 0.7552170594533284]

Training time: 0:00:41.216794

Evaluation result: {'f1': 0.5766458433445459, 'precision': 0.4344677769732078, 'recall': 0.8571428571428571, 'infer_time': 24.83352041244507}
Finished.
