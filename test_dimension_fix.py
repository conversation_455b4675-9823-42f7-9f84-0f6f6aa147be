#!/usr/bin/env python3
"""
🧪 维度修复测试
测试SignalQualityAssessment的动态维度适应是否有效
"""

import torch
import numpy as np
import sys
import os

# 添加src路径
sys.path.append('src')

def test_signal_quality_assessment_dynamic():
    """测试SignalQualityAssessment的动态维度适应"""
    print("🧪 测试SignalQualityAssessment动态维度适应...")
    
    try:
        from models.signal_processing import SignalQualityAssessment
        
        # 测试不同的输入维度
        test_cases = [
            (4, 50, 35),  # 期望维度
            (4, 50, 36),  # 问题维度（原来导致错误的）
            (4, 50, 32),  # 其他维度
            (4, 50, 64),  # 更大的维度
        ]
        
        success_count = 0
        
        for i, (B, T, D) in enumerate(test_cases):
            try:
                print(f"\n   测试用例 {i+1}: 输入形状 ({B}, {T}, {D})")
                
                # 创建测试数据
                test_input = torch.randn(B, T, D)
                
                # 创建质量评估器（使用不同的初始维度）
                assessor = SignalQualityAssessment(signal_dim=35)  # 故意使用不匹配的初始维度
                
                # 测试前向传播
                quality_scores = assessor(test_input)
                
                print(f"      ✅ 输出形状: {quality_scores.shape}")
                print(f"      ✅ 期望形状: ({B}, {T}, 1)")
                
                # 检查输出形状
                if quality_scores.shape == (B, T, 1):
                    print(f"      ✅ 测试用例 {i+1} 通过")
                    success_count += 1
                else:
                    print(f"      ❌ 测试用例 {i+1} 失败: 形状不匹配")
                    
            except Exception as e:
                print(f"      ❌ 测试用例 {i+1} 异常: {e}")
        
        print(f"\n   📊 动态维度适应测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"   ❌ 动态维度适应测试失败: {e}")
        return False

def test_enhanced_denoiser_robustness():
    """测试EnhancedDenoiser的鲁棒性"""
    print("🧪 测试EnhancedDenoiser鲁棒性...")
    
    try:
        from models.signal_processing import EnhancedDenoiser
        
        # 测试不同的输入维度
        test_cases = [
            (4, 50, 35),  # 期望维度
            (4, 50, 36),  # 问题维度
            (4, 46, 36),  # 原来导致错误的具体尺寸
        ]
        
        success_count = 0
        
        for i, (B, T, D) in enumerate(test_cases):
            try:
                print(f"\n   测试用例 {i+1}: 输入形状 ({B}, {T}, {D})")
                
                # 创建测试数据
                test_input = torch.randn(B, T, D)
                
                # 创建去噪器
                denoiser = EnhancedDenoiser(signal_dim=D)
                
                # 测试前向传播
                denoised_output, noise_level = denoiser(test_input)
                
                print(f"      ✅ 去噪输出形状: {denoised_output.shape}")
                if noise_level is not None:
                    print(f"      ✅ 噪声水平形状: {noise_level.shape}")
                
                # 检查输出形状
                if denoised_output.shape == test_input.shape:
                    print(f"      ✅ 测试用例 {i+1} 通过")
                    success_count += 1
                else:
                    print(f"      ❌ 测试用例 {i+1} 失败: 形状不匹配")
                    
            except Exception as e:
                print(f"      ❌ 测试用例 {i+1} 异常: {e}")
        
        print(f"\n   📊 EnhancedDenoiser鲁棒性测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"   ❌ EnhancedDenoiser鲁棒性测试失败: {e}")
        return False

def test_timesurl_integration_simulation():
    """模拟TimesURL集成测试"""
    print("🧪 模拟TimesURL集成测试...")
    
    try:
        from models.signal_processing import EnhancedDenoiser, SignalQualityAssessment
        
        # 模拟TimesURL中的实际使用场景
        # 假设input_dims=37（包含时间特征），feature_dims=36（排除时间特征）
        input_dims = 37
        feature_dims = input_dims - 1  # 36
        
        print(f"   模拟参数: input_dims={input_dims}, feature_dims={feature_dims}")
        
        # 创建组件（模拟TimesURL初始化）
        denoiser = EnhancedDenoiser(feature_dims, noise_level_predictor=True)
        assessor = SignalQualityAssessment(feature_dims)
        
        # 模拟训练过程中的数据
        B, T = 4, 50
        x_batch = torch.randn(B, T, input_dims)  # 包含时间特征的完整数据
        x_features_only = x_batch[..., :-1]     # 排除时间特征，形状为(B, T, 36)
        
        print(f"   x_batch形状: {x_batch.shape}")
        print(f"   x_features_only形状: {x_features_only.shape}")
        
        # 模拟adaptive_denoising的输出（假设形状不变）
        x_denoised_features = x_features_only  # 简化模拟
        
        # 测试增强去噪器
        enhanced_denoised, noise_level = denoiser(x_denoised_features)
        print(f"   增强去噪输出形状: {enhanced_denoised.shape}")
        
        # 测试质量评估器
        quality_scores = assessor(x_denoised_features)
        print(f"   质量分数形状: {quality_scores.shape}")
        
        # 模拟条件选择
        use_enhanced = quality_scores < 0.7
        final_features = torch.where(
            use_enhanced.expand_as(x_denoised_features),
            enhanced_denoised,
            x_denoised_features
        )
        print(f"   最终特征形状: {final_features.shape}")
        
        # 重新添加时间特征
        final_batch = torch.cat([final_features, x_batch[..., -1:]], dim=-1)
        print(f"   最终批次形状: {final_batch.shape}")
        
        # 检查所有形状是否正确
        if (enhanced_denoised.shape == x_denoised_features.shape and
            quality_scores.shape == (B, T, 1) and
            final_features.shape == x_denoised_features.shape and
            final_batch.shape == x_batch.shape):
            print("   ✅ TimesURL集成模拟测试通过")
            return True
        else:
            print("   ❌ TimesURL集成模拟测试失败: 形状不匹配")
            return False
            
    except Exception as e:
        print(f"   ❌ TimesURL集成模拟测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 === 维度修复测试 ===")
    print("🎯 目标: 验证SignalQualityAssessment动态维度适应修复")
    
    tests = [
        ("SignalQualityAssessment动态维度适应", test_signal_quality_assessment_dynamic),
        ("EnhancedDenoiser鲁棒性", test_enhanced_denoiser_robustness),
        ("TimesURL集成模拟", test_timesurl_integration_simulation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        if result:
            passed_tests += 1
        print(f"{'='*60}")
    
    print(f"\n📊 === 测试结果总结 ===")
    print(f"   通过测试: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("   ✅ 所有测试通过! 维度修复成功!")
        print("   🚀 可以运行优化实验了!")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("   ⚠️ 大部分测试通过，但仍有一些问题需要解决")
        return False
    else:
        print("   ❌ 多个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 下一步: 运行 basic_optimization_test.py 进行实际训练测试")
    else:
        print("\n⚠️ 需要进一步修复维度问题")
