#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DCRAD模型特征空间可视化实验
提取DCRAD编码器特征并进行t-SNE可视化分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
import torch
import json
import sys
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append('src')

class DCRADFeatureVisualizer:
    def __init__(self, model_path=None, random_seed=42):
        """
        初始化DCRAD特征可视化器
        
        Args:
            model_path: 训练好的DCRAD模型路径
            random_seed: 随机种子
        """
        self.model_path = model_path
        self.random_seed = random_seed
        self.model = None
        
        # 设置随机种子
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        
        # 设置可视化样式
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        sns.set_style("whitegrid", {'grid.alpha': 0.3})
    
    def load_model(self):
        """加载训练好的DCRAD模型"""
        if self.model_path and os.path.exists(self.model_path):
            print(f"📂 从 {self.model_path} 加载模型...")
            self.model = torch.load(self.model_path, map_location='cpu')
            self.model.eval()
            print("✅ 模型加载成功")
        else:
            print("⚠️ 未指定模型路径，将使用当前训练的模型")
            # 这里假设您有一个全局的模型变量或者从训练脚本中获取
            # self.model = your_trained_model
    
    def prepare_visualization_data(self, test_data, n_samples_per_class=200):
        """
        准备可视化数据集：正常样本 + CPO攻击 + RPO攻击
        
        Args:
            test_data: 测试数据
            n_samples_per_class: 每个类别的样本数
            
        Returns:
            viz_data: 可视化数据
            labels: 对应标签
        """
        print(f"\n📊 准备可视化数据集 (每类{n_samples_per_class}个样本)")
        
        # 根据您的数据集调整类别名称
        target_classes = {
            'Benign': ['genuine', 'normal', 'benign'],  # 正常样本的可能标签
            'CPO': ['cpo', 'constant_position', 'ConstPos'],  # CPO攻击的可能标签
            'RPO': ['rpo', 'random_position', 'RandomPos']   # RPO攻击的可能标签
        }
        
        visualization_data = []
        labels = []
        
        # 如果test_data是字典格式（来自dataloader）
        if isinstance(test_data, dict):
            if 'labels' in test_data:
                # 处理有标签的情况
                data_array = test_data['x']
                data_labels = test_data['labels']
            else:
                # 处理无标签的情况，假设都是正常样本
                data_array = test_data['x']
                data_labels = ['Benign'] * len(data_array)
        else:
            # 处理DataFrame格式
            data_array = test_data.drop(['attack_type'], axis=1).values if 'attack_type' in test_data.columns else test_data.values
            data_labels = test_data['attack_type'].values if 'attack_type' in test_data.columns else ['Benign'] * len(test_data)
        
        # 按类别采样数据
        for class_name, possible_labels in target_classes.items():
            class_indices = []
            
            # 查找匹配的标签
            for i, label in enumerate(data_labels):
                label_str = str(label).lower()
                if any(possible_label.lower() in label_str for possible_label in possible_labels):
                    class_indices.append(i)
            
            if class_indices:
                # 随机采样
                if len(class_indices) > n_samples_per_class:
                    selected_indices = np.random.choice(class_indices, n_samples_per_class, replace=False)
                else:
                    selected_indices = class_indices
                    print(f"⚠️ {class_name}类别样本不足，实际使用{len(selected_indices)}个样本")
                
                # 提取数据
                class_data = data_array[selected_indices]
                visualization_data.append(class_data)
                labels.extend([class_name] * len(selected_indices))
                
                print(f"  {class_name}: {len(selected_indices)} 样本")
            else:
                print(f"⚠️ 未找到{class_name}类别的样本")
        
        if not visualization_data:
            # 如果没有找到分类数据，使用所有数据作为正常样本
            print("📝 未找到分类标签，将所有数据视为正常样本")
            n_total = min(n_samples_per_class * 3, len(data_array))
            selected_indices = np.random.choice(len(data_array), n_total, replace=False)
            visualization_data = [data_array[selected_indices]]
            labels = ['Mixed'] * n_total
        else:
            # 合并所有类别的数据
            visualization_data = np.vstack(visualization_data)
        
        print(f"✅ 数据准备完成，总计 {len(labels)} 个样本")
        return visualization_data, labels
    
    def extract_features(self, data):
        """
        从DCRAD模型提取特征表示
        
        Args:
            data: 输入数据
            
        Returns:
            features: 提取的特征
        """
        print(f"\n🔍 从DCRAD模型提取特征...")
        
        if self.model is None:
            raise ValueError("模型未加载，请先调用load_model()或设置模型")
        
        self.model.eval()
        features = []
        
        # 批处理提取特征
        batch_size = 32
        n_batches = (len(data) + batch_size - 1) // batch_size
        
        with torch.no_grad():
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i+batch_size]
                
                # 转换为tensor
                if not isinstance(batch_data, torch.Tensor):
                    batch_tensor = torch.FloatTensor(batch_data)
                else:
                    batch_tensor = batch_data
                
                # 确保数据维度正确 (batch_size, seq_len, features)
                if len(batch_tensor.shape) == 2:
                    batch_tensor = batch_tensor.unsqueeze(1)  # 添加时间维度
                
                try:
                    # 尝试不同的特征提取方法
                    if hasattr(self.model, 'encoder'):
                        batch_features = self.model.encoder(batch_tensor)
                    elif hasattr(self.model, 'encode'):
                        batch_features = self.model.encode(batch_tensor)
                    elif hasattr(self.model, '_encode'):
                        batch_features = self.model._encode(batch_tensor)
                    else:
                        # 如果是TimesURL模型，可能需要特殊处理
                        # 假设模型有forward方法返回表示
                        batch_features = self.model(batch_tensor)
                        if isinstance(batch_features, tuple):
                            batch_features = batch_features[0]  # 取第一个输出
                    
                    # 处理特征维度
                    if len(batch_features.shape) > 2:
                        # 如果是3D特征 (batch, seq, dim)，进行平均池化
                        batch_features = torch.mean(batch_features, dim=1)
                    
                    features.append(batch_features.cpu().numpy())
                    
                except Exception as e:
                    print(f"⚠️ 批次 {i//batch_size + 1}/{n_batches} 特征提取失败: {e}")
                    # 使用随机特征作为备用
                    dummy_features = np.random.randn(len(batch_data), 128)
                    features.append(dummy_features)
        
        features = np.vstack(features)
        print(f"✅ 特征提取完成，维度: {features.shape}")
        
        return features
    
    def perform_tsne(self, features, labels, perplexity=30, learning_rate=200, n_iter=1000):
        """
        使用t-SNE进行降维
        
        Args:
            features: 高维特征
            labels: 对应标签
            perplexity: t-SNE困惑度参数
            learning_rate: 学习率
            n_iter: 迭代次数
            
        Returns:
            features_2d: 二维特征
        """
        print(f"\n📈 执行t-SNE降维 (perplexity={perplexity}, lr={learning_rate}, iter={n_iter})")
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # t-SNE降维
        tsne = TSNE(
            n_components=2,
            perplexity=min(perplexity, len(features)//4),  # 确保perplexity不会太大
            learning_rate=learning_rate,
            n_iter=n_iter,
            random_state=self.random_seed,
            verbose=0
        )
        
        features_2d = tsne.fit_transform(features_scaled)
        
        print(f"✅ t-SNE降维完成")
        return features_2d
    
    def visualize_features(self, features_2d, labels, save_path="dcrad_feature_visualization.png"):
        """
        可视化特征空间
        
        Args:
            features_2d: 二维特征
            labels: 对应标签
            save_path: 保存路径
        """
        print(f"\n🎨 生成特征空间可视化图...")
        
        # 设置颜色和标记
        unique_labels = list(set(labels))
        colors = ['#2E8B57', '#FF6347', '#4169E1', '#8B008B', '#FF8C00'][:len(unique_labels)]
        markers = ['o', '^', 's', 'D', 'v'][:len(unique_labels)]
        
        color_map = dict(zip(unique_labels, colors))
        marker_map = dict(zip(unique_labels, markers))
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        
        # 绘制散点图
        for label in unique_labels:
            label_indices = [i for i, l in enumerate(labels) if l == label]
            if label_indices:
                label_features = features_2d[label_indices]
                ax.scatter(
                    label_features[:, 0], 
                    label_features[:, 1],
                    c=color_map[label],
                    marker=marker_map[label],
                    s=60,
                    alpha=0.7,
                    label=f'{label} ({len(label_indices)})',
                    edgecolors='black',
                    linewidth=0.5
                )
        
        # 设置图形属性
        ax.set_title('DCRAD Feature Space Visualization (t-SNE)', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('t-SNE Dimension 1', fontsize=14)
        ax.set_ylabel('t-SNE Dimension 2', fontsize=14)
        ax.legend(frameon=True, fancybox=True, shadow=True, fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 计算并显示聚类质量
        if len(unique_labels) > 1:
            silhouette = self.calculate_silhouette_score(features_2d, labels)
            ax.text(0.02, 0.98, f'Silhouette Score: {silhouette:.3f}', 
                    transform=ax.transAxes, fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.9),
                    verticalalignment='top')
        
        # 添加统计信息
        stats_text = f'Total Samples: {len(labels)}\nFeature Dim: {features_2d.shape[1]}D → 2D'
        ax.text(0.98, 0.02, stats_text, 
                transform=ax.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8),
                verticalalignment='bottom', horizontalalignment='right')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 可视化图已保存: {save_path}")
        
        return fig
    
    def calculate_silhouette_score(self, features_2d, labels):
        """计算轮廓系数"""
        try:
            unique_labels = list(set(labels))
            if len(unique_labels) < 2:
                return 0.0
            
            label_map = {label: i for i, label in enumerate(unique_labels)}
            numeric_labels = [label_map[label] for label in labels]
            
            score = silhouette_score(features_2d, numeric_labels)
            return score
        except:
            return 0.0
    
    def analyze_feature_quality(self, features_2d, labels):
        """
        分析特征质量
        
        Args:
            features_2d: 二维特征
            labels: 对应标签
            
        Returns:
            analysis_results: 分析结果
        """
        print(f"\n📊 分析特征质量...")
        
        unique_labels = list(set(labels))
        analysis_results = {
            'n_samples': len(labels),
            'n_classes': len(unique_labels),
            'class_distribution': {label: labels.count(label) for label in unique_labels},
            'silhouette_score': self.calculate_silhouette_score(features_2d, labels)
        }
        
        if len(unique_labels) > 1:
            # 计算类内和类间距离
            class_centers = {}
            intra_distances = []
            
            # 计算每个类别的中心
            for label in unique_labels:
                label_indices = [i for i, l in enumerate(labels) if l == label]
                if label_indices:
                    label_features = features_2d[label_indices]
                    class_centers[label] = np.mean(label_features, axis=0)
                    
                    # 计算类内距离
                    center = class_centers[label]
                    distances = [np.linalg.norm(feat - center) for feat in label_features]
                    intra_distances.extend(distances)
            
            # 计算类间距离
            inter_distances = []
            for i, label1 in enumerate(unique_labels):
                for label2 in unique_labels[i+1:]:
                    if label1 in class_centers and label2 in class_centers:
                        distance = np.linalg.norm(class_centers[label1] - class_centers[label2])
                        inter_distances.append(distance)
            
            analysis_results.update({
                'avg_intra_distance': np.mean(intra_distances) if intra_distances else 0,
                'avg_inter_distance': np.mean(inter_distances) if inter_distances else 0,
                'separation_ratio': np.mean(inter_distances) / np.mean(intra_distances) if intra_distances and inter_distances else 0
            })
        
        # 打印分析结果
        print("📋 特征质量分析结果:")
        print(f"  样本总数: {analysis_results['n_samples']}")
        print(f"  类别数量: {analysis_results['n_classes']}")
        print(f"  类别分布: {analysis_results['class_distribution']}")
        print(f"  轮廓系数: {analysis_results['silhouette_score']:.4f}")
        
        if 'separation_ratio' in analysis_results:
            print(f"  平均类内距离: {analysis_results['avg_intra_distance']:.4f}")
            print(f"  平均类间距离: {analysis_results['avg_inter_distance']:.4f}")
            print(f"  分离度比值: {analysis_results['separation_ratio']:.4f}")
        
        return analysis_results
    
    def save_results(self, features_2d, labels, analysis_results, save_path="dcrad_visualization_results.json"):
        """保存实验结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'features_2d': features_2d.tolist(),
            'labels': labels,
            'analysis_results': analysis_results,
            'experiment_config': {
                'random_seed': self.random_seed,
                'model_path': self.model_path
            }
        }
        
        with open(save_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"💾 实验结果已保存: {save_path}")
    
    def run_complete_experiment(self, test_data, n_samples_per_class=200):
        """
        运行完整的特征可视化实验
        
        Args:
            test_data: 测试数据
            n_samples_per_class: 每个类别的样本数
            
        Returns:
            experiment_results: 实验结果
        """
        print("🚀 开始DCRAD特征空间可视化实验")
        print("="*60)
        
        # 1. 加载模型
        self.load_model()
        
        # 2. 准备数据
        viz_data, labels = self.prepare_visualization_data(test_data, n_samples_per_class)
        
        # 3. 提取特征
        features = self.extract_features(viz_data)
        
        # 4. t-SNE降维
        features_2d = self.perform_tsne(features, labels)
        
        # 5. 可视化
        fig = self.visualize_features(features_2d, labels)
        
        # 6. 分析特征质量
        analysis_results = self.analyze_feature_quality(features_2d, labels)
        
        # 7. 保存结果
        self.save_results(features_2d, labels, analysis_results)
        
        print("\n✅ 实验完成！")
        print("📁 输出文件:")
        print("  - dcrad_feature_visualization.png (可视化图)")
        print("  - dcrad_visualization_results.json (详细结果)")
        
        return {
            'features_2d': features_2d,
            'labels': labels,
            'analysis_results': analysis_results,
            'figure': fig
        }

# 使用示例和主函数
def main():
    """主函数 - 运行DCRAD特征可视化实验"""
    
    # 1. 初始化可视化器
    visualizer = DCRADFeatureVisualizer(
        model_path=None,  # 如果有保存的模型路径，在这里指定
        random_seed=42
    )
    
    # 2. 加载测试数据
    try:
        # 方法1: 从datautils加载
        from datautils import load_user_anomaly
        test_data, _, _, _ = load_user_anomaly('ConstPos')  # 根据您的数据集调整
        print("✅ 测试数据加载成功")
        
    except Exception as e:
        print(f"⚠️ 数据加载失败: {e}")
        print("请确保数据路径正确，或者手动加载测试数据")
        return
    
    # 3. 设置当前训练的模型（如果没有保存的模型文件）
    # visualizer.model = your_trained_dcrad_model  # 替换为您的模型变量
    
    # 4. 运行实验
    try:
        results = visualizer.run_complete_experiment(
            test_data=test_data,
            n_samples_per_class=200
        )
        
        print("\n🎉 DCRAD特征可视化实验成功完成！")
        
    except Exception as e:
        print(f"❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()