#!/usr/bin/env python3
"""
🔍 深度诊断去噪增噪数据质量问题
根本问题分析：对比学习失效的数据层面原因
"""

import numpy as np
import sys
import os

# 添加src路径
sys.path.append('src')

try:
    from datautils import load_user_anomaly
    from denoising_utils import generate_decl_training_data, adaptive_denoise
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在TimesURL项目根目录运行此脚本")
    sys.exit(1)

try:
    import torch
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    print("⚠️ PyTorch不可用，将跳过对比学习分析")
    TORCH_AVAILABLE = False

def analyze_denoising_quality():
    """分析去噪数据质量"""
    print("🔍 === 去噪数据质量分析 ===")

    try:
        # 加载数据 - 使用ConstPos数据集
        train_data, _, _, _ = load_user_anomaly('ConstPos')
        print(f"原始数据形状: {train_data['x'].shape}")

        # 生成DECL数据
        decl_data = generate_decl_training_data(train_data['x'], train_data['mask'])
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        print("尝试使用其他数据加载方法...")
        try:
            # 备用加载方法
            from datautils import load_dataset
            train_data, _, _, _ = load_dataset('ConstPos')
            decl_data = generate_decl_training_data(train_data['x'], train_data['mask'])
        except Exception as e2:
            print(f"❌ 备用方法也失败: {e2}")
            return False
    
    x_orig = decl_data['x']
    x_denoised = decl_data['x_denoised'] 
    x_noisy = decl_data['x_noisy']
    
    print(f"去噪数据形状: {x_denoised.shape}")
    print(f"增噪数据形状: {x_noisy.shape}")
    
    # 分析前5个样本的去噪效果
    signal_features = x_orig.shape[2] - 1  # 排除时间位置特征
    
    print("\n📊 去噪效果分析 (前5个样本):")
    denoising_effects = []
    
    for i in range(min(5, x_orig.shape[0])):
        # 计算去噪前后的差异
        orig_signal = x_orig[i, :, :signal_features]
        denoised_signal = x_denoised[i, :, :signal_features]
        
        # 计算MSE差异
        mse_diff = np.mean((orig_signal - denoised_signal) ** 2)
        
        # 计算信号变化幅度
        orig_std = np.std(orig_signal)
        denoised_std = np.std(denoised_signal)
        
        # 计算相关性
        correlation = np.corrcoef(orig_signal.flatten(), denoised_signal.flatten())[0, 1]
        
        denoising_effects.append({
            'sample': i,
            'mse_diff': mse_diff,
            'orig_std': orig_std,
            'denoised_std': denoised_std,
            'correlation': correlation
        })
        
        print(f"  样本{i}: MSE差异={mse_diff:.6f}, 相关性={correlation:.4f}, "
              f"标准差变化={orig_std:.4f}→{denoised_std:.4f}")
    
    # 分析去噪效果
    avg_mse = np.mean([e['mse_diff'] for e in denoising_effects])
    avg_correlation = np.mean([e['correlation'] for e in denoising_effects])
    
    print(f"\n📈 去噪效果总结:")
    print(f"  平均MSE差异: {avg_mse:.6f}")
    print(f"  平均相关性: {avg_correlation:.4f}")
    
    # 判断去噪质量
    if avg_mse < 1e-6:
        print("  ❌ 严重问题: 去噪几乎无效果，原始和去噪数据几乎相同!")
        return False
    elif avg_correlation > 0.99:
        print("  ⚠️ 问题: 去噪效果过弱，相关性过高")
        return False
    elif avg_correlation < 0.7:
        print("  ⚠️ 问题: 去噪过度，破坏了原始信号结构")
        return False
    else:
        print("  ✅ 去噪效果合理")
        return True

def analyze_noise_amplification():
    """分析噪声放大效果"""
    print("\n🔍 === 噪声放大效果分析 ===")

    try:
        # 加载数据 - 使用ConstPos数据集
        train_data, _, _, _ = load_user_anomaly('ConstPos')
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        try:
            from datautils import load_dataset
            train_data, _, _, _ = load_dataset('ConstPos')
        except Exception as e2:
            print(f"❌ 备用方法也失败: {e2}")
            return
    
    # 测试不同的噪声放大因子
    amplification_factors = [0.5, 1.0, 2.0, 3.0, 5.0]
    
    for alpha in amplification_factors:
        print(f"\n📊 测试噪声放大因子 α = {alpha}")

        try:
            decl_data = generate_decl_training_data(
                train_data['x'],
                train_data['mask'],
                noise_amplification_factor=alpha
            )
        except Exception as e:
            print(f"  ❌ 数据生成失败: {e}")
            continue
        
        x_orig = decl_data['x']
        x_denoised = decl_data['x_denoised']
        x_noisy = decl_data['x_noisy']
        
        # 分析噪声梯度
        signal_features = x_orig.shape[2] - 1
        
        # 计算平均噪声水平
        orig_noise = np.mean([np.std(x_orig[i, :, :signal_features]) for i in range(min(10, x_orig.shape[0]))])
        denoised_noise = np.mean([np.std(x_denoised[i, :, :signal_features]) for i in range(min(10, x_denoised.shape[0]))])
        noisy_noise = np.mean([np.std(x_noisy[i, :, :signal_features]) for i in range(min(10, x_noisy.shape[0]))])
        
        print(f"  噪声水平: 去噪({denoised_noise:.4f}) < 原始({orig_noise:.4f}) < 增噪({noisy_noise:.4f})")
        
        # 验证噪声梯度
        gradient_ok = denoised_noise <= orig_noise <= noisy_noise
        print(f"  噪声梯度: {'✅ 正确' if gradient_ok else '❌ 错误'}")
        
        # 计算区分度
        denoised_orig_diff = abs(orig_noise - denoised_noise)
        orig_noisy_diff = abs(noisy_noise - orig_noise)
        
        print(f"  区分度: 去噪-原始={denoised_orig_diff:.4f}, 原始-增噪={orig_noisy_diff:.4f}")
        
        if denoised_orig_diff < 0.01 and orig_noisy_diff < 0.01:
            print(f"  ❌ 严重问题: 三种数据几乎无区别，对比学习无效!")
        elif denoised_orig_diff < 0.05 or orig_noisy_diff < 0.05:
            print(f"  ⚠️ 问题: 数据区分度过低，对比学习效果差")
        else:
            print(f"  ✅ 数据区分度合理")

def analyze_contrastive_learning_effectiveness():
    """分析对比学习的有效性"""
    print("\n🔍 === 对比学习有效性分析 ===")

    if not TORCH_AVAILABLE:
        print("⚠️ PyTorch不可用，使用NumPy进行简化分析")
        return analyze_contrastive_learning_numpy()

    # 加载数据
    try:
        train_data, _, _, _ = load_user_anomaly('ConstPos')
    except Exception as e:
        try:
            from datautils import load_dataset
            train_data, _, _, _ = load_dataset('ConstPos')
        except Exception as e2:
            print(f"❌ 数据加载失败: {e2}")
            return False

    decl_data = generate_decl_training_data(train_data['x'], train_data['mask'])

    x_orig = torch.from_numpy(decl_data['x']).float()
    x_denoised = torch.from_numpy(decl_data['x_denoised']).float()
    x_noisy = torch.from_numpy(decl_data['x_noisy']).float()

    # 模拟编码器输出（使用简单的线性变换）
    B, T, D = x_orig.shape
    encoder_dim = 64

    # 创建简单的"编码器"
    encoder = torch.nn.Linear(D, encoder_dim)

    with torch.no_grad():
        z_orig = encoder(x_orig)
        z_denoised = encoder(x_denoised)
        z_noisy = encoder(x_noisy)

        # 归一化
        z_orig = F.normalize(z_orig, dim=-1)
        z_denoised = F.normalize(z_denoised, dim=-1)
        z_noisy = F.normalize(z_noisy, dim=-1)

        # 计算相似度
        pos_sim = F.cosine_similarity(z_orig, z_denoised, dim=-1)  # 原始-去噪相似度
        neg_sim = F.cosine_similarity(z_orig, z_noisy, dim=-1)     # 原始-增噪相似度

        # 分析相似度分布
        pos_sim_mean = pos_sim.mean().item()
        neg_sim_mean = neg_sim.mean().item()
        pos_sim_std = pos_sim.std().item()
        neg_sim_std = neg_sim.std().item()

        print(f"📊 相似度分析:")
        print(f"  正样本相似度 (原始-去噪): {pos_sim_mean:.4f} ± {pos_sim_std:.4f}")
        print(f"  负样本相似度 (原始-增噪): {neg_sim_mean:.4f} ± {neg_sim_std:.4f}")
        print(f"  相似度差异: {pos_sim_mean - neg_sim_mean:.4f}")

        # 判断对比学习有效性
        similarity_gap = pos_sim_mean - neg_sim_mean

        if similarity_gap < 0.01:
            print("  ❌ 严重问题: 正负样本相似度几乎相同，对比学习完全无效!")
            return False
        elif similarity_gap < 0.05:
            print("  ⚠️ 问题: 正负样本区分度过低，对比学习效果差")
            return False
        else:
            print("  ✅ 对比学习具有一定有效性")
            return True


def analyze_contrastive_learning_numpy():
    """使用NumPy进行对比学习分析"""
    # 加载数据
    try:
        train_data, _, _, _ = load_user_anomaly('ConstPos')
    except Exception as e:
        try:
            from datautils import load_dataset
            train_data, _, _, _ = load_dataset('ConstPos')
        except Exception as e2:
            print(f"❌ 数据加载失败: {e2}")
            return False

    decl_data = generate_decl_training_data(train_data['x'], train_data['mask'])

    x_orig = decl_data['x']
    x_denoised = decl_data['x_denoised']
    x_noisy = decl_data['x_noisy']

    # 使用简单的余弦相似度分析
    def cosine_similarity_numpy(a, b):
        """计算余弦相似度"""
        a_flat = a.reshape(a.shape[0], -1)
        b_flat = b.reshape(b.shape[0], -1)

        similarities = []
        for i in range(a_flat.shape[0]):
            dot_product = np.dot(a_flat[i], b_flat[i])
            norm_a = np.linalg.norm(a_flat[i])
            norm_b = np.linalg.norm(b_flat[i])
            if norm_a > 0 and norm_b > 0:
                sim = dot_product / (norm_a * norm_b)
            else:
                sim = 0.0
            similarities.append(sim)
        return np.array(similarities)

    # 计算相似度
    pos_sim = cosine_similarity_numpy(x_orig, x_denoised)  # 原始-去噪相似度
    neg_sim = cosine_similarity_numpy(x_orig, x_noisy)     # 原始-增噪相似度

    # 分析相似度分布
    pos_sim_mean = np.mean(pos_sim)
    neg_sim_mean = np.mean(neg_sim)
    pos_sim_std = np.std(pos_sim)
    neg_sim_std = np.std(neg_sim)

    print(f"📊 相似度分析 (NumPy版本):")
    print(f"  正样本相似度 (原始-去噪): {pos_sim_mean:.4f} ± {pos_sim_std:.4f}")
    print(f"  负样本相似度 (原始-增噪): {neg_sim_mean:.4f} ± {neg_sim_std:.4f}")
    print(f"  相似度差异: {pos_sim_mean - neg_sim_mean:.4f}")

    # 判断对比学习有效性
    similarity_gap = pos_sim_mean - neg_sim_mean

    if similarity_gap < 0.01:
        print("  ❌ 严重问题: 正负样本相似度几乎相同，对比学习完全无效!")
        return False
    elif similarity_gap < 0.05:
        print("  ⚠️ 问题: 正负样本区分度过低，对比学习效果差")
        return False
    else:
        print("  ✅ 对比学习具有一定有效性")
        return True

def main():
    """主诊断函数"""
    print("🏥 === 去噪增噪数据质量深度诊断 ===")
    print("目标: 找出对比学习失效的根本原因\n")
    
    # 1. 分析去噪质量
    denoising_ok = analyze_denoising_quality()
    
    # 2. 分析噪声放大效果
    analyze_noise_amplification()
    
    # 3. 分析对比学习有效性
    contrastive_ok = analyze_contrastive_learning_effectiveness()
    
    # 4. 综合诊断结论
    print("\n🎯 === 综合诊断结论 ===")
    
    if not denoising_ok:
        print("❌ 根本问题: 去噪算法失效，去噪数据与原始数据几乎相同")
        print("   这导致对比学习的正样本无效，三元组退化为二元组")
        print("   建议: 重新设计去噪算法或调整去噪参数")
    
    if not contrastive_ok:
        print("❌ 根本问题: 对比学习数据区分度过低")
        print("   正负样本在表征空间中几乎无法区分")
        print("   建议: 增加噪声放大因子或改进数据生成策略")
    
    if denoising_ok and contrastive_ok:
        print("✅ 数据质量基本合格，问题可能在模型架构或训练策略")
    else:
        print("🚨 数据质量存在根本性问题，必须优先解决!")
        
    print("\n💡 下一步建议:")
    print("1. 如果去噪失效: 调整adaptive_denoise参数或使用更强的去噪方法")
    print("2. 如果区分度低: 增加noise_amplification_factor到5.0或更高")
    print("3. 如果都有问题: 考虑完全重新设计数据生成策略")

if __name__ == "__main__":
    main()
