Dataset: ConstPos
Arguments: Namespace(batch_size=16, dataset='ConstPos', epochs=25, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.05, lambda_decl=0.3, lambda_sep=0.5, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.0005, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, patience=8, precision_target=0.65, recall_target=0.75, reconstruct_target='denoised', repr_dims=320, run_name='optimized_test_denoised', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=0.5, use_focal_loss=True)
✅ 已设置随机种子: 42
Loading data... done
(480, 50, 37)

🔍 === TRAINING FLOW INVESTIGATION ===
📊 Input train_data type: <class 'dict'>
📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])
🎯 DECL Mode Detected: True
🔄 === DECL TRAINING BRANCH ===
📈 Original data shape: (480, 50, 37)
📈 Denoised data shape: (480, 50, 37)
📈 Noisy data shape: (480, 50, 37)
📈 Mask shape: (480, 50, 36)
📈 Batch size: 16
🎯 初始化Gumbel-Softmax门控网络...
🚀 初始化基于Gumbel-Softmax的自适应去噪系统...
✅ 自适应去噪模块已创建
   - 输入维度: 36
   - 去噪器数量: 7
   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']
✅ 门控网络初始化完成
🌡️ 温度退火设置: 初始2.0 -> 最终0.1
📊 DataLoader created with 30 batches
📊 Expected batches per epoch: 30
🔧 DECL Optimizer created with 64 parameters (包含门控网络)

🎯 === TRAINING PARAMETERS INVESTIGATION ===
📊 Input n_epochs: 25
📊 Input n_iters: None
📊 Final training parameters:
   - n_epochs: 25
   - n_iters: None
   - max_epochs: 25
   - batches_per_epoch: 30
   - self.n_epochs: 0
   - self.n_iters: 0

🚀 === STARTING TRAINING LOOP ===

📅 Starting Epoch 0
🔍 Check termination: n_epochs=25, self.n_epochs=0
🔄 === DECL TRAINING LOOP (Epoch 0) ===
🔄 Batch 20/30
   📊 Loss: Total=1.949 (Rec=1.880, Trip=0.229)
🔄 Batch 30/30
   📊 Loss: Total=0.888 (Rec=0.870, Trip=0.060)
📊 DECL Epoch 0 completed: 30 batches processed

📊 === EPOCH 0 COMPLETED ===
   ⏱️ Epoch time: 3.496s
   ⏱️ Total time so far: 3.496s
   📈 Batches processed: 30
   📈 Average loss: 1.017671
   📈 Total iterations so far: 30
   🔍 Interrupted flag: False
Epoch #0: loss=1.0176714758078258
   📈 Incremented self.n_epochs to: 1

📅 Starting Epoch 1
🔍 Check termination: n_epochs=25, self.n_epochs=1
🔄 === DECL TRAINING LOOP (Epoch 1) ===
🔄 Batch 20/30
   📊 Loss: Total=0.757 (Rec=0.745, Trip=0.039)
🔄 Batch 30/30
   📊 Loss: Total=1.065 (Rec=1.053, Trip=0.041)
📊 DECL Epoch 1 completed: 30 batches processed

📊 === EPOCH 1 COMPLETED ===
   ⏱️ Epoch time: 2.730s
   ⏱️ Total time so far: 6.226s
   📈 Batches processed: 30
   📈 Average loss: 0.937630
   📈 Total iterations so far: 60
   🔍 Interrupted flag: False
Epoch #1: loss=0.9376297513643901
   📈 Incremented self.n_epochs to: 2

📅 Starting Epoch 2
🔍 Check termination: n_epochs=25, self.n_epochs=2
🔄 === DECL TRAINING LOOP (Epoch 2) ===
🔄 Batch 20/30
   📊 Loss: Total=1.178 (Rec=1.166, Trip=0.039)
🔄 Batch 30/30
   📊 Loss: Total=0.941 (Rec=0.930, Trip=0.035)
📊 DECL Epoch 2 completed: 30 batches processed

📊 === EPOCH 2 COMPLETED ===
   ⏱️ Epoch time: 2.639s
   ⏱️ Total time so far: 8.865s
   📈 Batches processed: 30
   📈 Average loss: 0.829899
   📈 Total iterations so far: 90
   🔍 Interrupted flag: False
Epoch #2: loss=0.8298993090788523
   📈 Incremented self.n_epochs to: 3

📅 Starting Epoch 3
🔍 Check termination: n_epochs=25, self.n_epochs=3
🔄 === DECL TRAINING LOOP (Epoch 3) ===
🔄 Batch 20/30
   📊 Loss: Total=0.829 (Rec=0.818, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=1.117 (Rec=1.106, Trip=0.036)
📊 DECL Epoch 3 completed: 30 batches processed

📊 === EPOCH 3 COMPLETED ===
   ⏱️ Epoch time: 2.816s
   ⏱️ Total time so far: 11.682s
   📈 Batches processed: 30
   📈 Average loss: 0.918120
   📈 Total iterations so far: 120
   🔍 Interrupted flag: False
Epoch #3: loss=0.9181200424830119
   📈 Incremented self.n_epochs to: 4

📅 Starting Epoch 4
🔍 Check termination: n_epochs=25, self.n_epochs=4
🔄 === DECL TRAINING LOOP (Epoch 4) ===
🔄 Batch 20/30
   📊 Loss: Total=0.749 (Rec=0.739, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=0.832 (Rec=0.822, Trip=0.033)
📊 DECL Epoch 4 completed: 30 batches processed

📊 === EPOCH 4 COMPLETED ===
   ⏱️ Epoch time: 2.734s
   ⏱️ Total time so far: 14.415s
   📈 Batches processed: 30
   📈 Average loss: 0.800867
   📈 Total iterations so far: 150
   🔍 Interrupted flag: False
Epoch #4: loss=0.8008666654427846
   📈 Incremented self.n_epochs to: 5

📅 Starting Epoch 5
🔍 Check termination: n_epochs=25, self.n_epochs=5
🔄 === DECL TRAINING LOOP (Epoch 5) ===
🔄 Batch 20/30
   📊 Loss: Total=0.877 (Rec=0.867, Trip=0.034)
🔄 Batch 30/30
   📊 Loss: Total=0.738 (Rec=0.728, Trip=0.032)
📊 DECL Epoch 5 completed: 30 batches processed

📊 === EPOCH 5 COMPLETED ===
   ⏱️ Epoch time: 2.823s
   ⏱️ Total time so far: 17.239s
   📈 Batches processed: 30
   📈 Average loss: 0.834810
   📈 Total iterations so far: 180
   🔍 Interrupted flag: False
Epoch #5: loss=0.8348100513219834
   📈 Incremented self.n_epochs to: 6

📅 Starting Epoch 6
🔍 Check termination: n_epochs=25, self.n_epochs=6
🔄 === DECL TRAINING LOOP (Epoch 6) ===
🔄 Batch 20/30
   📊 Loss: Total=1.905 (Rec=1.895, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=0.796 (Rec=0.786, Trip=0.032)
📊 DECL Epoch 6 completed: 30 batches processed

📊 === EPOCH 6 COMPLETED ===
   ⏱️ Epoch time: 2.730s
   ⏱️ Total time so far: 19.969s
   📈 Batches processed: 30
   📈 Average loss: 0.849145
   📈 Total iterations so far: 210
   🔍 Interrupted flag: False
Epoch #6: loss=0.8491447349389394
   📈 Incremented self.n_epochs to: 7

📅 Starting Epoch 7
🔍 Check termination: n_epochs=25, self.n_epochs=7
🔄 === DECL TRAINING LOOP (Epoch 7) ===
🔄 Batch 20/30
   📊 Loss: Total=0.746 (Rec=0.737, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.512 (Rec=0.501, Trip=0.035)
📊 DECL Epoch 7 completed: 30 batches processed

📊 === EPOCH 7 COMPLETED ===
   ⏱️ Epoch time: 3.012s
   ⏱️ Total time so far: 22.981s
   📈 Batches processed: 30
   📈 Average loss: 0.768713
   📈 Total iterations so far: 240
   🔍 Interrupted flag: False
Epoch #7: loss=0.7687131504217783
   📈 Incremented self.n_epochs to: 8

📅 Starting Epoch 8
🔍 Check termination: n_epochs=25, self.n_epochs=8
🔄 === DECL TRAINING LOOP (Epoch 8) ===
🔄 Batch 20/30
   📊 Loss: Total=0.471 (Rec=0.461, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=0.687 (Rec=0.677, Trip=0.032)
📊 DECL Epoch 8 completed: 30 batches processed

📊 === EPOCH 8 COMPLETED ===
   ⏱️ Epoch time: 2.939s
   ⏱️ Total time so far: 25.920s
   📈 Batches processed: 30
   📈 Average loss: 0.768062
   📈 Total iterations so far: 270
   🔍 Interrupted flag: False
Epoch #8: loss=0.7680616001288096
   📈 Incremented self.n_epochs to: 9

📅 Starting Epoch 9
🔍 Check termination: n_epochs=25, self.n_epochs=9
🔄 === DECL TRAINING LOOP (Epoch 9) ===
🔄 Batch 20/30
   📊 Loss: Total=0.832 (Rec=0.799, Trip=0.108)
🔄 Batch 30/30
   📊 Loss: Total=0.867 (Rec=0.852, Trip=0.051)
📊 DECL Epoch 9 completed: 30 batches processed

📊 === EPOCH 9 COMPLETED ===
   ⏱️ Epoch time: 3.252s
   ⏱️ Total time so far: 29.172s
   📈 Batches processed: 30
   📈 Average loss: 0.840813
   📈 Total iterations so far: 300
   🔍 Interrupted flag: False
Epoch #9: loss=0.8408131609360378
   📈 Incremented self.n_epochs to: 10

📅 Starting Epoch 10
🔍 Check termination: n_epochs=25, self.n_epochs=10
🔄 === DECL TRAINING LOOP (Epoch 10) ===
🔄 Batch 20/30
   📊 Loss: Total=0.794 (Rec=0.785, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.655 (Rec=0.646, Trip=0.032)
📊 DECL Epoch 10 completed: 30 batches processed

📊 === EPOCH 10 COMPLETED ===
   ⏱️ Epoch time: 3.369s
   ⏱️ Total time so far: 32.541s
   📈 Batches processed: 30
   📈 Average loss: 0.806494
   📈 Total iterations so far: 330
   🔍 Interrupted flag: False
Epoch #10: loss=0.8064941803614298
   📈 Incremented self.n_epochs to: 11

📅 Starting Epoch 11
🔍 Check termination: n_epochs=25, self.n_epochs=11
🔄 === DECL TRAINING LOOP (Epoch 11) ===
🔄 Batch 20/30
   📊 Loss: Total=0.782 (Rec=0.772, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.758 (Rec=0.748, Trip=0.032)
📊 DECL Epoch 11 completed: 30 batches processed

📊 === EPOCH 11 COMPLETED ===
   ⏱️ Epoch time: 3.496s
   ⏱️ Total time so far: 36.037s
   📈 Batches processed: 30
   📈 Average loss: 0.808531
   📈 Total iterations so far: 360
   🔍 Interrupted flag: False
Epoch #11: loss=0.8085313121477763
   📈 Incremented self.n_epochs to: 12

📅 Starting Epoch 12
🔍 Check termination: n_epochs=25, self.n_epochs=12
🔄 === DECL TRAINING LOOP (Epoch 12) ===
🔄 Batch 20/30
   📊 Loss: Total=0.811 (Rec=0.801, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=1.251 (Rec=1.242, Trip=0.031)
📊 DECL Epoch 12 completed: 30 batches processed

📊 === EPOCH 12 COMPLETED ===
   ⏱️ Epoch time: 3.841s
   ⏱️ Total time so far: 39.878s
   📈 Batches processed: 30
   📈 Average loss: 0.823068
   📈 Total iterations so far: 390
   🔍 Interrupted flag: False
Epoch #12: loss=0.823068305850029
   📈 Incremented self.n_epochs to: 13

📅 Starting Epoch 13
🔍 Check termination: n_epochs=25, self.n_epochs=13
🔄 === DECL TRAINING LOOP (Epoch 13) ===
🔄 Batch 20/30
   📊 Loss: Total=0.911 (Rec=0.902, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.707 (Rec=0.697, Trip=0.031)
📊 DECL Epoch 13 completed: 30 batches processed

📊 === EPOCH 13 COMPLETED ===
   ⏱️ Epoch time: 3.244s
   ⏱️ Total time so far: 43.122s
   📈 Batches processed: 30
   📈 Average loss: 0.793571
   📈 Total iterations so far: 420
   🔍 Interrupted flag: False
Epoch #13: loss=0.7935711503028869
   📈 Incremented self.n_epochs to: 14

📅 Starting Epoch 14
🔍 Check termination: n_epochs=25, self.n_epochs=14
🔄 === DECL TRAINING LOOP (Epoch 14) ===
🔄 Batch 20/30
   📊 Loss: Total=0.884 (Rec=0.874, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.797 (Rec=0.787, Trip=0.031)
📊 DECL Epoch 14 completed: 30 batches processed

📊 === EPOCH 14 COMPLETED ===
   ⏱️ Epoch time: 2.808s
   ⏱️ Total time so far: 45.930s
   📈 Batches processed: 30
   📈 Average loss: 0.791969
   📈 Total iterations so far: 450
   🔍 Interrupted flag: False
Epoch #14: loss=0.7919691522916158
   📈 Incremented self.n_epochs to: 15

📅 Starting Epoch 15
🔍 Check termination: n_epochs=25, self.n_epochs=15
🔄 === DECL TRAINING LOOP (Epoch 15) ===
🔄 Batch 20/30
   📊 Loss: Total=0.650 (Rec=0.641, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.784 (Rec=0.774, Trip=0.032)
📊 DECL Epoch 15 completed: 30 batches processed

📊 === EPOCH 15 COMPLETED ===
   ⏱️ Epoch time: 2.868s
   ⏱️ Total time so far: 48.798s
   📈 Batches processed: 30
   📈 Average loss: 0.767677
   📈 Total iterations so far: 480
   🔍 Interrupted flag: False
Epoch #15: loss=0.7676766306161881
   📈 Incremented self.n_epochs to: 16

📅 Starting Epoch 16
🔍 Check termination: n_epochs=25, self.n_epochs=16
🔄 === DECL TRAINING LOOP (Epoch 16) ===
🔄 Batch 20/30
   📊 Loss: Total=0.692 (Rec=0.682, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.900 (Rec=0.890, Trip=0.031)
📊 DECL Epoch 16 completed: 30 batches processed

📊 === EPOCH 16 COMPLETED ===
   ⏱️ Epoch time: 2.683s
   ⏱️ Total time so far: 51.480s
   📈 Batches processed: 30
   📈 Average loss: 0.767594
   📈 Total iterations so far: 510
   🔍 Interrupted flag: False
Epoch #16: loss=0.7675944914420446
   📈 Incremented self.n_epochs to: 17

📅 Starting Epoch 17
🔍 Check termination: n_epochs=25, self.n_epochs=17
🔄 === DECL TRAINING LOOP (Epoch 17) ===
🔄 Batch 20/30
   📊 Loss: Total=0.817 (Rec=0.808, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=1.038 (Rec=1.028, Trip=0.032)
📊 DECL Epoch 17 completed: 30 batches processed

📊 === EPOCH 17 COMPLETED ===
   ⏱️ Epoch time: 2.684s
   ⏱️ Total time so far: 54.165s
   📈 Batches processed: 30
   📈 Average loss: 0.789997
   📈 Total iterations so far: 540
   🔍 Interrupted flag: False
Epoch #17: loss=0.7899965544541677
   📈 Incremented self.n_epochs to: 18

📅 Starting Epoch 18
🔍 Check termination: n_epochs=25, self.n_epochs=18
🔄 === DECL TRAINING LOOP (Epoch 18) ===
🔄 Batch 20/30
   📊 Loss: Total=0.605 (Rec=0.596, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.887 (Rec=0.877, Trip=0.033)
📊 DECL Epoch 18 completed: 30 batches processed

📊 === EPOCH 18 COMPLETED ===
   ⏱️ Epoch time: 2.570s
   ⏱️ Total time so far: 56.735s
   📈 Batches processed: 30
   📈 Average loss: 0.774529
   📈 Total iterations so far: 570
   🔍 Interrupted flag: False
Epoch #18: loss=0.7745286822319031
   📈 Incremented self.n_epochs to: 19

📅 Starting Epoch 19
🔍 Check termination: n_epochs=25, self.n_epochs=19
🔄 === DECL TRAINING LOOP (Epoch 19) ===
🔄 Batch 20/30
   📊 Loss: Total=0.650 (Rec=0.641, Trip=0.030)
🔄 Batch 30/30
   📊 Loss: Total=0.349 (Rec=0.340, Trip=0.031)
📊 DECL Epoch 19 completed: 30 batches processed

📊 === EPOCH 19 COMPLETED ===
   ⏱️ Epoch time: 2.643s
   ⏱️ Total time so far: 59.377s
   📈 Batches processed: 30
   📈 Average loss: 0.723771
   📈 Total iterations so far: 600
   🔍 Interrupted flag: False
Epoch #19: loss=0.7237706532080969
   📈 Incremented self.n_epochs to: 20

📅 Starting Epoch 20
🔍 Check termination: n_epochs=25, self.n_epochs=20
🔄 === DECL TRAINING LOOP (Epoch 20) ===
🔄 Batch 20/30
   📊 Loss: Total=0.809 (Rec=0.800, Trip=0.030)
🔄 Batch 30/30
   📊 Loss: Total=1.143 (Rec=1.133, Trip=0.031)
📊 DECL Epoch 20 completed: 30 batches processed

📊 === EPOCH 20 COMPLETED ===
   ⏱️ Epoch time: 2.658s
   ⏱️ Total time so far: 62.035s
   📈 Batches processed: 30
   📈 Average loss: 0.733542
   📈 Total iterations so far: 630
   🔍 Interrupted flag: False
Epoch #20: loss=0.7335420568784078
   📈 Incremented self.n_epochs to: 21

📅 Starting Epoch 21
🔍 Check termination: n_epochs=25, self.n_epochs=21
🔄 === DECL TRAINING LOOP (Epoch 21) ===
🔄 Batch 20/30
   📊 Loss: Total=0.734 (Rec=0.725, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.579 (Rec=0.570, Trip=0.031)
📊 DECL Epoch 21 completed: 30 batches processed

📊 === EPOCH 21 COMPLETED ===
   ⏱️ Epoch time: 2.684s
   ⏱️ Total time so far: 64.719s
   📈 Batches processed: 30
   📈 Average loss: 0.800298
   📈 Total iterations so far: 660
   🔍 Interrupted flag: False
Epoch #21: loss=0.8002983649571737
   📈 Incremented self.n_epochs to: 22

📅 Starting Epoch 22
🔍 Check termination: n_epochs=25, self.n_epochs=22
🔄 === DECL TRAINING LOOP (Epoch 22) ===
🔄 Batch 20/30
   📊 Loss: Total=0.693 (Rec=0.684, Trip=0.030)
🔄 Batch 30/30
   📊 Loss: Total=0.640 (Rec=0.630, Trip=0.031)
📊 DECL Epoch 22 completed: 30 batches processed

📊 === EPOCH 22 COMPLETED ===
   ⏱️ Epoch time: 2.663s
   ⏱️ Total time so far: 67.382s
   📈 Batches processed: 30
   📈 Average loss: 0.700766
   📈 Total iterations so far: 690
   🔍 Interrupted flag: False
Epoch #22: loss=0.7007656216621398
   📈 Incremented self.n_epochs to: 23

📅 Starting Epoch 23
🔍 Check termination: n_epochs=25, self.n_epochs=23
🔄 === DECL TRAINING LOOP (Epoch 23) ===
🔄 Batch 20/30
   📊 Loss: Total=0.863 (Rec=0.854, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.917 (Rec=0.908, Trip=0.031)
📊 DECL Epoch 23 completed: 30 batches processed

📊 === EPOCH 23 COMPLETED ===
   ⏱️ Epoch time: 2.572s
   ⏱️ Total time so far: 69.953s
   📈 Batches processed: 30
   📈 Average loss: 0.775028
   📈 Total iterations so far: 720
   🔍 Interrupted flag: False
Epoch #23: loss=0.7750279774268468
   📈 Incremented self.n_epochs to: 24

📅 Starting Epoch 24
🔍 Check termination: n_epochs=25, self.n_epochs=24
🔄 === DECL TRAINING LOOP (Epoch 24) ===
🔄 Batch 20/30
   📊 Loss: Total=0.847 (Rec=0.837, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.734 (Rec=0.725, Trip=0.031)
📊 DECL Epoch 24 completed: 30 batches processed

📊 === EPOCH 24 COMPLETED ===
   ⏱️ Epoch time: 2.764s
   ⏱️ Total time so far: 72.717s
   📈 Batches processed: 30
   📈 Average loss: 0.740620
   📈 Total iterations so far: 750
   🔍 Interrupted flag: False
Epoch #24: loss=0.740620278318723
   📈 Incremented self.n_epochs to: 25

📅 Starting Epoch 25
🔍 Check termination: n_epochs=25, self.n_epochs=25
🛑 Breaking due to epoch limit: 25 >= 25

🏁 === TRAINING COMPLETED ===
   ⏱️ Total training time: 72.717s
   📈 Total epochs completed: 25
   📈 Total iterations completed: 750
   📈 Final loss: 0.740620278318723
   📊 Loss history: [1.0176714758078258, 0.9376297513643901, 0.8298993090788523, 0.9181200424830119, 0.8008666654427846, 0.8348100513219834, 0.8491447349389394, 0.7687131504217783, 0.7680616001288096, 0.8408131609360378, 0.8064941803614298, 0.8085313121477763, 0.823068305850029, 0.7935711503028869, 0.7919691522916158, 0.7676766306161881, 0.7675944914420446, 0.7899965544541677, 0.7745286822319031, 0.7237706532080969, 0.7335420568784078, 0.8002983649571737, 0.7007656216621398, 0.7750279774268468, 0.740620278318723]

Training time: 0:01:13.148426

Evaluation result: {'f1': 0.5235602094240839, 'precision': 0.39257673090649536, 'recall': 0.7857142857142857, 'infer_time': 27.330515384674072}
Finished.
