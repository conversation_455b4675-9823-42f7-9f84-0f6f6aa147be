Dataset: ConstPos
Arguments: Namespace(batch_size=16, dataset='ConstPos', epochs=25, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.05, lambda_decl=0.3, lambda_sep=0.5, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.0005, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, patience=8, precision_target=0.65, recall_target=0.75, reconstruct_target='original', repr_dims=320, run_name='optimized_test_original', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=0.5, use_focal_loss=True)
✅ 已设置随机种子: 42
Loading data... done
(480, 50, 37)

🔍 === TRAINING FLOW INVESTIGATION ===
📊 Input train_data type: <class 'dict'>
📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])
🎯 DECL Mode Detected: True
🔄 === DECL TRAINING BRANCH ===
📈 Original data shape: (480, 50, 37)
📈 Denoised data shape: (480, 50, 37)
📈 Noisy data shape: (480, 50, 37)
📈 Mask shape: (480, 50, 36)
📈 Batch size: 16
🎯 初始化Gumbel-Softmax门控网络...
🚀 初始化基于Gumbel-Softmax的自适应去噪系统...
✅ 自适应去噪模块已创建
   - 输入维度: 36
   - 去噪器数量: 7
   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']
✅ 门控网络初始化完成
🌡️ 温度退火设置: 初始2.0 -> 最终0.1
📊 DataLoader created with 30 batches
📊 Expected batches per epoch: 30
🔧 DECL Optimizer created with 64 parameters (包含门控网络)

🎯 === TRAINING PARAMETERS INVESTIGATION ===
📊 Input n_epochs: 25
📊 Input n_iters: None
📊 Final training parameters:
   - n_epochs: 25
   - n_iters: None
   - max_epochs: 25
   - batches_per_epoch: 30
   - self.n_epochs: 0
   - self.n_iters: 0

🚀 === STARTING TRAINING LOOP ===

📅 Starting Epoch 0
🔍 Check termination: n_epochs=25, self.n_epochs=0
🔄 === DECL TRAINING LOOP (Epoch 0) ===
🔄 Batch 20/30
   📊 Loss: Total=1.949 (Rec=1.880, Trip=0.229)
🔄 Batch 30/30
   📊 Loss: Total=0.888 (Rec=0.870, Trip=0.060)
📊 DECL Epoch 0 completed: 30 batches processed

📊 === EPOCH 0 COMPLETED ===
   ⏱️ Epoch time: 3.927s
   ⏱️ Total time so far: 3.927s
   📈 Batches processed: 30
   📈 Average loss: 1.062230
   📈 Total iterations so far: 30
   🔍 Interrupted flag: False
Epoch #0: loss=1.0622299273808797
   📈 Incremented self.n_epochs to: 1

📅 Starting Epoch 1
🔍 Check termination: n_epochs=25, self.n_epochs=1
🔄 === DECL TRAINING LOOP (Epoch 1) ===
🔄 Batch 20/30
   📊 Loss: Total=0.758 (Rec=0.746, Trip=0.039)
🔄 Batch 30/30
   📊 Loss: Total=1.062 (Rec=1.050, Trip=0.041)
📊 DECL Epoch 1 completed: 30 batches processed

📊 === EPOCH 1 COMPLETED ===
   ⏱️ Epoch time: 2.807s
   ⏱️ Total time so far: 6.734s
   📈 Batches processed: 30
   📈 Average loss: 0.946725
   📈 Total iterations so far: 60
   🔍 Interrupted flag: False
Epoch #1: loss=0.9467251161734264
   📈 Incremented self.n_epochs to: 2

📅 Starting Epoch 2
🔍 Check termination: n_epochs=25, self.n_epochs=2
🔄 === DECL TRAINING LOOP (Epoch 2) ===
🔄 Batch 20/30
   📊 Loss: Total=1.179 (Rec=1.167, Trip=0.039)
🔄 Batch 30/30
   📊 Loss: Total=0.942 (Rec=0.932, Trip=0.035)
📊 DECL Epoch 2 completed: 30 batches processed

📊 === EPOCH 2 COMPLETED ===
   ⏱️ Epoch time: 2.815s
   ⏱️ Total time so far: 9.549s
   📈 Batches processed: 30
   📈 Average loss: 0.897948
   📈 Total iterations so far: 90
   🔍 Interrupted flag: False
Epoch #2: loss=0.897947778304418
   📈 Incremented self.n_epochs to: 3

📅 Starting Epoch 3
🔍 Check termination: n_epochs=25, self.n_epochs=3
🔄 === DECL TRAINING LOOP (Epoch 3) ===
🔄 Batch 20/30
   📊 Loss: Total=0.829 (Rec=0.819, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=1.119 (Rec=1.108, Trip=0.036)
📊 DECL Epoch 3 completed: 30 batches processed

📊 === EPOCH 3 COMPLETED ===
   ⏱️ Epoch time: 2.802s
   ⏱️ Total time so far: 12.351s
   📈 Batches processed: 30
   📈 Average loss: 0.918414
   📈 Total iterations so far: 120
   🔍 Interrupted flag: False
Epoch #3: loss=0.9184140463670095
   📈 Incremented self.n_epochs to: 4

📅 Starting Epoch 4
🔍 Check termination: n_epochs=25, self.n_epochs=4
🔄 === DECL TRAINING LOOP (Epoch 4) ===
🔄 Batch 20/30
   📊 Loss: Total=0.750 (Rec=0.739, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=0.835 (Rec=0.825, Trip=0.033)
📊 DECL Epoch 4 completed: 30 batches processed

📊 === EPOCH 4 COMPLETED ===
   ⏱️ Epoch time: 2.547s
   ⏱️ Total time so far: 14.898s
   📈 Batches processed: 30
   📈 Average loss: 0.860569
   📈 Total iterations so far: 150
   🔍 Interrupted flag: False
Epoch #4: loss=0.8605691730976105
   📈 Incremented self.n_epochs to: 5

📅 Starting Epoch 5
🔍 Check termination: n_epochs=25, self.n_epochs=5
🔄 === DECL TRAINING LOOP (Epoch 5) ===
🔄 Batch 20/30
   📊 Loss: Total=0.874 (Rec=0.863, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=0.738 (Rec=0.728, Trip=0.032)
📊 DECL Epoch 5 completed: 30 batches processed

📊 === EPOCH 5 COMPLETED ===
   ⏱️ Epoch time: 3.023s
   ⏱️ Total time so far: 17.922s
   📈 Batches processed: 30
   📈 Average loss: 0.862733
   📈 Total iterations so far: 180
   🔍 Interrupted flag: False
Epoch #5: loss=0.8627334634462992
   📈 Incremented self.n_epochs to: 6

📅 Starting Epoch 6
🔍 Check termination: n_epochs=25, self.n_epochs=6
🔄 === DECL TRAINING LOOP (Epoch 6) ===
🔄 Batch 20/30
   📊 Loss: Total=1.907 (Rec=1.896, Trip=0.035)
🔄 Batch 30/30
   📊 Loss: Total=0.795 (Rec=0.785, Trip=0.033)
📊 DECL Epoch 6 completed: 30 batches processed

📊 === EPOCH 6 COMPLETED ===
   ⏱️ Epoch time: 3.316s
   ⏱️ Total time so far: 21.237s
   📈 Batches processed: 30
   📈 Average loss: 0.912796
   📈 Total iterations so far: 210
   🔍 Interrupted flag: False
Epoch #6: loss=0.9127957900365193
   📈 Incremented self.n_epochs to: 7

📅 Starting Epoch 7
🔍 Check termination: n_epochs=25, self.n_epochs=7
🔄 === DECL TRAINING LOOP (Epoch 7) ===
🔄 Batch 20/30
   📊 Loss: Total=0.744 (Rec=0.735, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.802 (Rec=0.791, Trip=0.035)
📊 DECL Epoch 7 completed: 30 batches processed

📊 === EPOCH 7 COMPLETED ===
   ⏱️ Epoch time: 3.079s
   ⏱️ Total time so far: 24.316s
   📈 Batches processed: 30
   📈 Average loss: 0.841814
   📈 Total iterations so far: 240
   🔍 Interrupted flag: False
Epoch #7: loss=0.8418136060237884
   📈 Incremented self.n_epochs to: 8

📅 Starting Epoch 8
🔍 Check termination: n_epochs=25, self.n_epochs=8
🔄 === DECL TRAINING LOOP (Epoch 8) ===
🔄 Batch 20/30
   📊 Loss: Total=0.927 (Rec=0.917, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=0.689 (Rec=0.679, Trip=0.033)
📊 DECL Epoch 8 completed: 30 batches processed

📊 === EPOCH 8 COMPLETED ===
   ⏱️ Epoch time: 2.820s
   ⏱️ Total time so far: 27.137s
   📈 Batches processed: 30
   📈 Average loss: 0.819184
   📈 Total iterations so far: 270
   🔍 Interrupted flag: False
Epoch #8: loss=0.8191840728123982
   📈 Incremented self.n_epochs to: 9

📅 Starting Epoch 9
🔍 Check termination: n_epochs=25, self.n_epochs=9
🔄 === DECL TRAINING LOOP (Epoch 9) ===
🔄 Batch 20/30
   📊 Loss: Total=0.814 (Rec=0.804, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=0.862 (Rec=0.852, Trip=0.032)
📊 DECL Epoch 9 completed: 30 batches processed

📊 === EPOCH 9 COMPLETED ===
   ⏱️ Epoch time: 2.743s
   ⏱️ Total time so far: 29.879s
   📈 Batches processed: 30
   📈 Average loss: 0.863396
   📈 Total iterations so far: 300
   🔍 Interrupted flag: False
Epoch #9: loss=0.8633960882822672
   📈 Incremented self.n_epochs to: 10

📅 Starting Epoch 10
🔍 Check termination: n_epochs=25, self.n_epochs=10
🔄 === DECL TRAINING LOOP (Epoch 10) ===
🔄 Batch 20/30
   📊 Loss: Total=0.795 (Rec=0.785, Trip=0.032)
🔄 Batch 30/30
   📊 Loss: Total=0.655 (Rec=0.645, Trip=0.032)
📊 DECL Epoch 10 completed: 30 batches processed

📊 === EPOCH 10 COMPLETED ===
   ⏱️ Epoch time: 2.752s
   ⏱️ Total time so far: 32.631s
   📈 Batches processed: 30
   📈 Average loss: 0.816347
   📈 Total iterations so far: 330
   🔍 Interrupted flag: False
Epoch #10: loss=0.81634667913119
   📈 Incremented self.n_epochs to: 11

📅 Starting Epoch 11
🔍 Check termination: n_epochs=25, self.n_epochs=11
🔄 === DECL TRAINING LOOP (Epoch 11) ===
🔄 Batch 20/30
   📊 Loss: Total=0.780 (Rec=0.770, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.754 (Rec=0.744, Trip=0.032)
📊 DECL Epoch 11 completed: 30 batches processed

📊 === EPOCH 11 COMPLETED ===
   ⏱️ Epoch time: 2.713s
   ⏱️ Total time so far: 35.345s
   📈 Batches processed: 30
   📈 Average loss: 0.819557
   📈 Total iterations so far: 360
   🔍 Interrupted flag: False
Epoch #11: loss=0.8195573707421621
   📈 Incremented self.n_epochs to: 12

📅 Starting Epoch 12
🔍 Check termination: n_epochs=25, self.n_epochs=12
🔄 === DECL TRAINING LOOP (Epoch 12) ===
🔄 Batch 20/30
   📊 Loss: Total=0.807 (Rec=0.797, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=1.248 (Rec=1.238, Trip=0.031)
📊 DECL Epoch 12 completed: 30 batches processed

📊 === EPOCH 12 COMPLETED ===
   ⏱️ Epoch time: 2.762s
   ⏱️ Total time so far: 38.107s
   📈 Batches processed: 30
   📈 Average loss: 0.845777
   📈 Total iterations so far: 390
   🔍 Interrupted flag: False
Epoch #12: loss=0.8457769612471263
   📈 Incremented self.n_epochs to: 13

📅 Starting Epoch 13
🔍 Check termination: n_epochs=25, self.n_epochs=13
🔄 === DECL TRAINING LOOP (Epoch 13) ===
🔄 Batch 20/30
   📊 Loss: Total=0.910 (Rec=0.900, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.713 (Rec=0.703, Trip=0.031)
📊 DECL Epoch 13 completed: 30 batches processed

📊 === EPOCH 13 COMPLETED ===
   ⏱️ Epoch time: 2.909s
   ⏱️ Total time so far: 41.017s
   📈 Batches processed: 30
   📈 Average loss: 0.807254
   📈 Total iterations so far: 420
   🔍 Interrupted flag: False
Epoch #13: loss=0.8072543919086457
   📈 Incremented self.n_epochs to: 14

📅 Starting Epoch 14
🔍 Check termination: n_epochs=25, self.n_epochs=14
🔄 === DECL TRAINING LOOP (Epoch 14) ===
🔄 Batch 20/30
   📊 Loss: Total=0.886 (Rec=0.877, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.797 (Rec=0.788, Trip=0.031)
📊 DECL Epoch 14 completed: 30 batches processed

📊 === EPOCH 14 COMPLETED ===
   ⏱️ Epoch time: 3.618s
   ⏱️ Total time so far: 44.635s
   📈 Batches processed: 30
   📈 Average loss: 0.791061
   📈 Total iterations so far: 450
   🔍 Interrupted flag: False
Epoch #14: loss=0.7910607079664866
   📈 Incremented self.n_epochs to: 15

📅 Starting Epoch 15
🔍 Check termination: n_epochs=25, self.n_epochs=15
🔄 === DECL TRAINING LOOP (Epoch 15) ===
🔄 Batch 20/30
   📊 Loss: Total=0.647 (Rec=0.638, Trip=0.033)
🔄 Batch 30/30
   📊 Loss: Total=0.782 (Rec=0.773, Trip=0.032)
📊 DECL Epoch 15 completed: 30 batches processed

📊 === EPOCH 15 COMPLETED ===
   ⏱️ Epoch time: 5.006s
   ⏱️ Total time so far: 49.641s
   📈 Batches processed: 30
   📈 Average loss: 0.811896
   📈 Total iterations so far: 480
   🔍 Interrupted flag: False
Epoch #15: loss=0.8118962347507477
   📈 Incremented self.n_epochs to: 16

📅 Starting Epoch 16
🔍 Check termination: n_epochs=25, self.n_epochs=16
🔄 === DECL TRAINING LOOP (Epoch 16) ===
🔄 Batch 20/30
   📊 Loss: Total=0.693 (Rec=0.684, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.894 (Rec=0.884, Trip=0.032)
📊 DECL Epoch 16 completed: 30 batches processed

📊 === EPOCH 16 COMPLETED ===
   ⏱️ Epoch time: 2.876s
   ⏱️ Total time so far: 52.517s
   📈 Batches processed: 30
   📈 Average loss: 0.787628
   📈 Total iterations so far: 510
   🔍 Interrupted flag: False
Epoch #16: loss=0.7876282831033071
   📈 Incremented self.n_epochs to: 17

📅 Starting Epoch 17
🔍 Check termination: n_epochs=25, self.n_epochs=17
🔄 === DECL TRAINING LOOP (Epoch 17) ===
🔄 Batch 20/30
   📊 Loss: Total=0.828 (Rec=0.817, Trip=0.036)
🔄 Batch 30/30
   📊 Loss: Total=1.038 (Rec=1.028, Trip=0.031)
📊 DECL Epoch 17 completed: 30 batches processed

📊 === EPOCH 17 COMPLETED ===
   ⏱️ Epoch time: 2.726s
   ⏱️ Total time so far: 55.242s
   📈 Batches processed: 30
   📈 Average loss: 0.805183
   📈 Total iterations so far: 540
   🔍 Interrupted flag: False
Epoch #17: loss=0.8051826775074005
   📈 Incremented self.n_epochs to: 18

📅 Starting Epoch 18
🔍 Check termination: n_epochs=25, self.n_epochs=18
🔄 === DECL TRAINING LOOP (Epoch 18) ===
🔄 Batch 20/30
   📊 Loss: Total=0.610 (Rec=0.601, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.883 (Rec=0.873, Trip=0.031)
📊 DECL Epoch 18 completed: 30 batches processed

📊 === EPOCH 18 COMPLETED ===
   ⏱️ Epoch time: 2.864s
   ⏱️ Total time so far: 58.106s
   📈 Batches processed: 30
   📈 Average loss: 0.793771
   📈 Total iterations so far: 570
   🔍 Interrupted flag: False
Epoch #18: loss=0.7937711536884308
   📈 Incremented self.n_epochs to: 19

📅 Starting Epoch 19
🔍 Check termination: n_epochs=25, self.n_epochs=19
🔄 === DECL TRAINING LOOP (Epoch 19) ===
🔄 Batch 20/30
   📊 Loss: Total=0.651 (Rec=0.642, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.736 (Rec=0.727, Trip=0.031)
📊 DECL Epoch 19 completed: 30 batches processed

📊 === EPOCH 19 COMPLETED ===
   ⏱️ Epoch time: 2.817s
   ⏱️ Total time so far: 60.923s
   📈 Batches processed: 30
   📈 Average loss: 0.777575
   📈 Total iterations so far: 600
   🔍 Interrupted flag: False
Epoch #19: loss=0.777574646472931
   📈 Incremented self.n_epochs to: 20

📅 Starting Epoch 20
🔍 Check termination: n_epochs=25, self.n_epochs=20
🔄 === DECL TRAINING LOOP (Epoch 20) ===
🔄 Batch 20/30
   📊 Loss: Total=0.805 (Rec=0.796, Trip=0.030)
🔄 Batch 30/30
   📊 Loss: Total=1.130 (Rec=1.121, Trip=0.031)
📊 DECL Epoch 20 completed: 30 batches processed

📊 === EPOCH 20 COMPLETED ===
   ⏱️ Epoch time: 2.776s
   ⏱️ Total time so far: 63.699s
   📈 Batches processed: 30
   📈 Average loss: 0.750470
   📈 Total iterations so far: 630
   🔍 Interrupted flag: False
Epoch #20: loss=0.7504698117574056
   📈 Incremented self.n_epochs to: 21

📅 Starting Epoch 21
🔍 Check termination: n_epochs=25, self.n_epochs=21
🔄 === DECL TRAINING LOOP (Epoch 21) ===
🔄 Batch 20/30
   📊 Loss: Total=0.735 (Rec=0.725, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.581 (Rec=0.572, Trip=0.031)
📊 DECL Epoch 21 completed: 30 batches processed

📊 === EPOCH 21 COMPLETED ===
   ⏱️ Epoch time: 2.618s
   ⏱️ Total time so far: 66.316s
   📈 Batches processed: 30
   📈 Average loss: 0.809802
   📈 Total iterations so far: 660
   🔍 Interrupted flag: False
Epoch #21: loss=0.8098015745480855
   📈 Incremented self.n_epochs to: 22

📅 Starting Epoch 22
🔍 Check termination: n_epochs=25, self.n_epochs=22
🔄 === DECL TRAINING LOOP (Epoch 22) ===
🔄 Batch 20/30
   📊 Loss: Total=0.690 (Rec=0.681, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.632 (Rec=0.623, Trip=0.031)
📊 DECL Epoch 22 completed: 30 batches processed

📊 === EPOCH 22 COMPLETED ===
   ⏱️ Epoch time: 2.557s
   ⏱️ Total time so far: 68.873s
   📈 Batches processed: 30
   📈 Average loss: 0.738554
   📈 Total iterations so far: 690
   🔍 Interrupted flag: False
Epoch #22: loss=0.7385544339815776
   📈 Incremented self.n_epochs to: 23

📅 Starting Epoch 23
🔍 Check termination: n_epochs=25, self.n_epochs=23
🔄 === DECL TRAINING LOOP (Epoch 23) ===
🔄 Batch 20/30
   📊 Loss: Total=0.860 (Rec=0.850, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.914 (Rec=0.905, Trip=0.031)
📊 DECL Epoch 23 completed: 30 batches processed

📊 === EPOCH 23 COMPLETED ===
   ⏱️ Epoch time: 2.474s
   ⏱️ Total time so far: 71.348s
   📈 Batches processed: 30
   📈 Average loss: 0.782882
   📈 Total iterations so far: 720
   🔍 Interrupted flag: False
Epoch #23: loss=0.7828821241855621
   📈 Incremented self.n_epochs to: 24

📅 Starting Epoch 24
🔍 Check termination: n_epochs=25, self.n_epochs=24
🔄 === DECL TRAINING LOOP (Epoch 24) ===
🔄 Batch 20/30
   📊 Loss: Total=0.847 (Rec=0.837, Trip=0.031)
🔄 Batch 30/30
   📊 Loss: Total=0.732 (Rec=0.723, Trip=0.031)
📊 DECL Epoch 24 completed: 30 batches processed

📊 === EPOCH 24 COMPLETED ===
   ⏱️ Epoch time: 2.969s
   ⏱️ Total time so far: 74.317s
   📈 Batches processed: 30
   📈 Average loss: 0.765673
   📈 Total iterations so far: 750
   🔍 Interrupted flag: False
Epoch #24: loss=0.7656728347142537
   📈 Incremented self.n_epochs to: 25

📅 Starting Epoch 25
🔍 Check termination: n_epochs=25, self.n_epochs=25
🛑 Breaking due to epoch limit: 25 >= 25

🏁 === TRAINING COMPLETED ===
   ⏱️ Total training time: 74.317s
   📈 Total epochs completed: 25
   📈 Total iterations completed: 750
   📈 Final loss: 0.7656728347142537
   📊 Loss history: [1.0622299273808797, 0.9467251161734264, 0.897947778304418, 0.9184140463670095, 0.8605691730976105, 0.8627334634462992, 0.9127957900365193, 0.8418136060237884, 0.8191840728123982, 0.8633960882822672, 0.81634667913119, 0.8195573707421621, 0.8457769612471263, 0.8072543919086457, 0.7910607079664866, 0.8118962347507477, 0.7876282831033071, 0.8051826775074005, 0.7937711536884308, 0.777574646472931, 0.7504698117574056, 0.8098015745480855, 0.7385544339815776, 0.7828821241855621, 0.7656728347142537]

Training time: 0:01:14.958883

Evaluation result: {'f1': 0.5263157894736842, 'precision': 0.39568345323741005, 'recall': 0.7857142857142857, 'infer_time': 28.69011425971985}
Finished.
