[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (优化版)", "success": false, "error": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\nTraceback (most recent call last):\n  File \"src/train.py\", line 181, in <module>\n    loss_log = model.fit(\n  File \"/mnt/d/WorkSpace/TimesURL/src/timesurl.py\", line 463, in fit\n    self.recent_losses.append(loss.item())\nAttributeError: 'TimesURL' object has no attribute 'recent_losses'\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}, {"reconstruct_target": "denoised", "description": "方案B: 重建去噪信号 (优化版)", "success": false, "error": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\nTraceback (most recent call last):\n  File \"src/train.py\", line 181, in <module>\n    loss_log = model.fit(\n  File \"/mnt/d/WorkSpace/TimesURL/src/timesurl.py\", line 463, in fit\n    self.recent_losses.append(loss.item())\nAttributeError: 'TimesURL' object has no attribute 'recent_losses'\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}]