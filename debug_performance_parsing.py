#!/usr/bin/env python3
"""
🔍 性能指标解析调试脚本
专门用于调试为什么性能指标解析失败
"""

import subprocess
import json
import re
import sys
import os
from datetime import datetime

def run_single_debug_test():
    """运行单个调试测试"""
    
    print("🔍 === 性能指标解析调试 ===")
    print("🎯 目标: 找出为什么F1、精度、召回率都是0")
    
    cmd = [
        "python", "src/train.py", 
        "ConstPos", "debug_test",
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", "5",  # 只训练5个epoch用于快速调试
        "--batch-size", "8",
        "--lr", "0.001",
        "--gpu", "0",
        "--reconstruct_target", "original",
        # 使用基本参数，不使用可能有问题的优化参数
        "--lambda_decl", "0.1",
        "--temp_contrast", "1.0",
        "--lambda_align", "0.1"
    ]
    
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5分钟超时
        
        if result.returncode != 0:
            print(f"❌ 训练失败:")
            print(f"   stderr: {result.stderr}")
            print(f"   stdout: {result.stdout}")
            return False
        
        output = result.stdout
        print(f"✅ 训练完成，开始详细分析输出...")
        
        # 保存完整输出
        with open("debug_full_output.txt", "w", encoding="utf-8") as f:
            f.write("=== STDOUT ===\n")
            f.write(output)
            f.write("\n=== STDERR ===\n")
            f.write(result.stderr)
        
        print(f"📁 完整输出已保存到: debug_full_output.txt")
        
        # 分析输出结构
        lines = output.strip().split('\n')
        print(f"\n📊 输出分析:")
        print(f"   总行数: {len(lines)}")
        print(f"   前10行:")
        for i, line in enumerate(lines[:10]):
            print(f"      {i+1:2d}: {line}")
        
        print(f"   后10行:")
        for i, line in enumerate(lines[-10:]):
            print(f"      {len(lines)-10+i+1:2d}: {line}")
        
        # 搜索可能的性能指标
        print(f"\n🔍 搜索性能指标:")
        
        # 搜索包含数字的行
        numeric_lines = []
        for i, line in enumerate(lines):
            if re.search(r'\d+\.\d+', line):
                numeric_lines.append((i+1, line))
        
        print(f"   包含数字的行 (前20行):")
        for line_num, line in numeric_lines[:20]:
            print(f"      {line_num:3d}: {line}")
        
        # 搜索特定关键词
        keywords = ['f1', 'precision', 'recall', 'F1', 'Precision', 'Recall', 'score', 'metric', 'result']
        print(f"\n🔍 搜索关键词:")
        for keyword in keywords:
            matches = []
            for i, line in enumerate(lines):
                if keyword.lower() in line.lower():
                    matches.append((i+1, line))
            
            if matches:
                print(f"   '{keyword}' 出现在:")
                for line_num, line in matches[:5]:  # 只显示前5个匹配
                    print(f"      {line_num:3d}: {line}")
            else:
                print(f"   '{keyword}' 未找到")
        
        # 尝试不同的正则表达式
        print(f"\n🔍 尝试不同的正则表达式:")
        patterns = [
            (r"f1[:\s]*([0-9.]+)", "f1基本模式"),
            (r"F1[:\s]*([0-9.]+)", "F1大写模式"),
            (r"'f1'[:\s]*([0-9.]+)", "f1引号模式"),
            (r"f1_score[:\s]*([0-9.]+)", "f1_score模式"),
            (r"precision[:\s]*([0-9.]+)", "precision基本模式"),
            (r"recall[:\s]*([0-9.]+)", "recall基本模式"),
            (r"\{.*f1.*:.*([0-9.]+)", "JSON f1模式"),
            (r"\{.*precision.*:.*([0-9.]+)", "JSON precision模式"),
            (r"\{.*recall.*:.*([0-9.]+)", "JSON recall模式"),
        ]
        
        for pattern, description in patterns:
            matches = re.findall(pattern, output, re.IGNORECASE)
            if matches:
                print(f"   {description}: 找到 {matches}")
            else:
                print(f"   {description}: 未找到")
        
        # 检查是否有异常检测相关的输出
        print(f"\n🔍 检查异常检测相关输出:")
        anomaly_keywords = ['anomaly', 'detection', 'threshold', 'score', 'evaluation']
        for keyword in anomaly_keywords:
            count = output.lower().count(keyword.lower())
            print(f"   '{keyword}' 出现次数: {count}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 调试测试超时")
        return False
    except Exception as e:
        print(f"❌ 调试测试异常: {e}")
        return False

def analyze_existing_output():
    """分析现有的输出文件"""
    print("\n🔍 === 分析现有输出文件 ===")
    
    # 查找最近的调试输出文件
    debug_files = []
    for filename in os.listdir('.'):
        if filename.startswith('debug_output_') and filename.endswith('.txt'):
            debug_files.append(filename)
    
    if not debug_files:
        print("   ❌ 没有找到现有的调试输出文件")
        return
    
    # 分析最新的文件
    latest_file = max(debug_files, key=os.path.getmtime)
    print(f"   📁 分析文件: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   📊 文件大小: {len(content)} 字符")
        
        # 搜索性能指标
        patterns = [
            (r"f1[:\s]*([0-9.]+)", "F1分数"),
            (r"precision[:\s]*([0-9.]+)", "精度"),
            (r"recall[:\s]*([0-9.]+)", "召回率"),
        ]
        
        for pattern, name in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"   ✅ {name}: {matches}")
            else:
                print(f"   ❌ {name}: 未找到")
        
        # 显示文件的最后几行
        lines = content.strip().split('\n')
        print(f"   📝 文件最后10行:")
        for line in lines[-10:]:
            print(f"      {line}")
            
    except Exception as e:
        print(f"   ❌ 分析文件失败: {e}")

def main():
    """主调试流程"""
    print("🔍 === 性能指标解析调试工具 ===")
    print("🎯 目标: 找出为什么性能指标都是0")
    
    # 首先分析现有输出
    analyze_existing_output()
    
    # 运行新的调试测试
    print(f"\n" + "="*60)
    success = run_single_debug_test()
    
    if success:
        print(f"\n✅ 调试测试完成!")
        print(f"📋 下一步:")
        print(f"   1. 检查 debug_full_output.txt 了解完整输出")
        print(f"   2. 根据分析结果修复性能指标解析")
        print(f"   3. 确认异常检测评估是否正常运行")
    else:
        print(f"\n❌ 调试测试失败!")
        print(f"📋 需要检查:")
        print(f"   1. 训练过程是否正常")
        print(f"   2. 数据加载是否成功")
        print(f"   3. 评估过程是否执行")

if __name__ == "__main__":
    main()
