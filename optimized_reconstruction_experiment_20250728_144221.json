[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (优化版)", "success": false, "error": "usage: train.py [-h] --loader LOADER [--gpu GPU] [--batch-size BATCH_SIZE]\n                [--lr LR] [--repr-dims REPR_DIMS]\n                [--max-train-length MAX_TRAIN_LENGTH] [--iters ITERS]\n                [--epochs EPOCHS] [--save-every SAVE_EVERY] [--seed SEED]\n                [--max-threads MAX_THREADS] [--eval] [--sgd] [--load_tp]\n                [--temp TEMP] [--lmd LMD] [--lambda_decl LAMBDA_DECL]\n                [--lambda_sep LAMBDA_SEP] [--gumbel_tau GUMBEL_TAU]\n                [--irregular IRREGULAR] [--segment_num SEGMENT_NUM]\n                [--mask_ratio_per_seg MASK_RATIO_PER_SEG]\n                [--tc_timesteps TC_TIMESTEPS]\n                [--noise_amplification_factor NOISE_AMPLIFICATION_FACTOR]\n                [--reconstruct_target {original,denoised}]\n                [--temp_contrast TEMP_CONTRAST] [--lambda_align LAMBDA_ALIGN]\n                dataset run_name\ntrain.py: error: unrecognized arguments: --use_focal_loss True --precision_target 0.65 --recall_target 0.75 --patience 8\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}, {"reconstruct_target": "denoised", "description": "方案B: 重建去噪信号 (优化版)", "success": false, "error": "usage: train.py [-h] --loader LOADER [--gpu GPU] [--batch-size BATCH_SIZE]\n                [--lr LR] [--repr-dims REPR_DIMS]\n                [--max-train-length MAX_TRAIN_LENGTH] [--iters ITERS]\n                [--epochs EPOCHS] [--save-every SAVE_EVERY] [--seed SEED]\n                [--max-threads MAX_THREADS] [--eval] [--sgd] [--load_tp]\n                [--temp TEMP] [--lmd LMD] [--lambda_decl LAMBDA_DECL]\n                [--lambda_sep LAMBDA_SEP] [--gumbel_tau GUMBEL_TAU]\n                [--irregular IRREGULAR] [--segment_num SEGMENT_NUM]\n                [--mask_ratio_per_seg MASK_RATIO_PER_SEG]\n                [--tc_timesteps TC_TIMESTEPS]\n                [--noise_amplification_factor NOISE_AMPLIFICATION_FACTOR]\n                [--reconstruct_target {original,denoised}]\n                [--temp_contrast TEMP_CONTRAST] [--lambda_align LAMBDA_ALIGN]\n                dataset run_name\ntrain.py: error: unrecognized arguments: --use_focal_loss True --precision_target 0.65 --recall_target 0.75 --patience 8\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}]