# 开发规范和规则

- SignalQualityAssessment模块存在维度不匹配问题：期望输入维度35但实际收到36，需要动态适应输入维度
- 优化实验能运行但结果都是0，需要修复：1.性能指标解析失败 2.训练稳定性问题 3.实验脚本逻辑错误
- 优化失败：F1从0.560降到0.4880，精度从0.416降到0.3706。需要回退到基线配置，进行系统性的单一变量测试，避免过度工程化
- 参数不一致问题已修复：将src/train.py和run_user_anomaly.py的默认参数统一为最佳设置 - batch_size=16, lr=0.001, epochs=20。这解决了不同脚本性能差异的根本原因（之前lr=0.0001导致性能差）
- 已实施梯度稳定化和损失平滑机制：1)添加梯度裁剪max_norm=1.0防止梯度爆炸；2)实现指数移动平均损失平滑，窗口大小5，平滑系数0.3；3)降低学习率到原来的50%，权重衰减降至1e-4；4)在训练输出中显示平滑后的损失值以便观察稳定性
- 已实施对比学习优化解决三元组损失停滞：1)降低温度参数从1.0到0.5增强对比效果；2)增加方向对齐权重从0.1到0.3；3)优化特征归一化策略减少梯度信息丢失；4)使用交叉熵损失替代不稳定的log-sum-exp；5)添加梯度监控检测学习停滞；6)优化方向对齐损失使用点积计算
- 发现并修复阈值策略的根本问题：虽然对比学习已修复(pos_sim>neg_sim)，但异常检测阈值仍使用错误的统计伪标签。已实施基于分位数的保守阈值策略，针对无标签异常检测场景，使用85-97%分位数范围，并基于分布偏斜度自适应调整
