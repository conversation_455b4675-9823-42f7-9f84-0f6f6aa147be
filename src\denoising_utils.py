import numpy as np
import torch
from scipy import signal
from sklearn.preprocessing import StandardScaler

from scipy.signal import butter, filtfilt
from pykalman import KalmanFilter
import pywt


def denoise_with_moving_average(data, window_size=5):
    """
    使用滑动平均对数据进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        window_size (int): 滑动窗口大小
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    if data.ndim == 3:
        # 对每个样本和特征独立处理
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                # 跳过时间位置向量（最后一个特征）
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    # 对信号特征进行滑动平均
                    padded = np.pad(data[i, :, j], (window_size//2, window_size//2), mode='edge')
                    denoised_data[i, :, j] = np.convolve(padded, np.ones(window_size)/window_size, mode='valid')
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def denoise_with_gaussian_filter(data, sigma=1.0):
    """
    使用高斯滤波器进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        sigma (float): 高斯核的标准差
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    from scipy.ndimage import gaussian_filter1d
    
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    denoised_data[i, :, j] = gaussian_filter1d(data[i, :, j], sigma=sigma)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def denoise_with_savgol_filter(data, window_length=5, polyorder=2):
    """
    使用Savitzky-Golay滤波器进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        window_length (int): 滤波器窗口长度（必须为奇数）
        polyorder (int): 多项式拟合的阶数
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    if window_length % 2 == 0:
        window_length += 1  # 确保窗口长度为奇数
    
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    if data.shape[1] >= window_length:
                        denoised_data[i, :, j] = signal.savgol_filter(data[i, :, j], window_length, polyorder)
                    else:
                        denoised_data[i, :, j] = data[i, :, j]  # 如果数据太短则不滤波
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def compute_snr(original, denoised):
    """
    计算信噪比 (SNR)
    """
    signal_power = np.mean(original ** 2)
    noise = original - denoised
    noise_power = np.mean(noise ** 2)
    if signal_power == 0:
        return 0 if noise_power == 0 else -100
    if noise_power == 0:
        return 100
    return 10 * np.log10(signal_power / noise_power)

def compute_smoothness(denoised):
    """
    计算平滑度 (一阶差分方差的负值, 更高分数表示更平滑)
    """
    if denoised.shape[1] < 2:
        return 0
    diff = np.diff(denoised, axis=1)
    variance = np.mean(diff ** 2, axis=1)
    return -np.mean(variance)  # 负值以便更高分数更好

def denoise_with_kalman(data, process_noise=0.1, measurement_noise=1.0):
    """
    使用 Kalman 滤波器进行去噪 (经典于 IoV 轨迹平滑)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    kf = KalmanFilter(transition_matrices=[1], observation_matrices=[1],
                                      transition_covariance=process_noise,
                                      observation_covariance=measurement_noise)
                    measurements = data[i, :, j]
                    denoised, _ = kf.smooth(measurements)
                    denoised_data[i, :, j] = denoised.flatten()
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_wavelet(data, wavelet='db4', level=1):
    """
    使用小波变换进行去噪 (适合 IoV 非平稳噪声)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    coeffs = pywt.wavedec(data[i, :, j], wavelet, level=level)
                    sigma = np.median(np.abs(coeffs[-level])) / 0.6745
                    thresh = sigma * np.sqrt(2 * np.log(len(data[i, :, j])))
                    coeffs[1:] = [pywt.threshold(c, thresh, mode='soft') for c in coeffs[1:]]
                    denoised_data[i, :, j] = pywt.waverec(coeffs, wavelet)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_median_filter(data, kernel_size=3):
    """
    使用中值滤波进行去噪 (有效去除 IoV 尖峰噪声)
    """
    from scipy.ndimage import median_filter
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    denoised_data[i, :, j] = median_filter(data[i, :, j], size=kernel_size)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_lowpass(data, cutoff=0.1, fs=1.0, order=5):
    """
    使用 Butterworth 低通滤波进行去噪 (适合 IoV 高频干扰)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    b, a = butter(order, cutoff, fs=fs, btype='low', analog=False)
                    denoised_data[i, :, j] = filtfilt(b, a, data[i, :, j])
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def adaptive_denoise(data, methods=None, weights={'snr': 0.7, 'smoothness': 0.3}, 
                    fallback_method='moving_average'):
    """
    自适应去噪: 尝试多种方法, 选择分数最高的
    
    Args:
        data (np.ndarray): 输入数据 (n_samples, n_timesteps, n_features)
        methods (list): 要尝试的方法列表
        weights (dict): 分数权重
        fallback_method (str): 备用方法
        
    Returns:
        np.ndarray: 最佳去噪数据
    """
    if methods is None:
        methods = [
            ('moving_average', {'window_size': 3}),
            ('moving_average', {'window_size': 5}),
            ('gaussian', {'sigma': 0.8}),
            ('gaussian', {'sigma': 1.2}),
            ('savgol', {'window_length': 5, 'polyorder': 2}),
            ('kalman', {'process_noise': 0.05, 'measurement_noise': 0.8}),
            ('median', {'kernel_size': 3}),
        ]
    
    best_denoised = np.zeros_like(data)
    
    for i in range(data.shape[0]):
        best_score = -np.inf
        best_method_data = None
        
        for method_name, params in methods:
            try:
                # 应用去噪方法
                if method_name == 'moving_average':
                    candidate = denoise_with_moving_average(data[i:i+1], **params)
                elif method_name == 'gaussian':
                    candidate = denoise_with_gaussian_filter(data[i:i+1], **params)
                elif method_name == 'savgol':
                    candidate = denoise_with_savgol_filter(data[i:i+1], **params)
                elif method_name == 'kalman':
                    candidate = denoise_with_kalman(data[i:i+1], **params)
                elif method_name == 'median':
                    candidate = denoise_with_median_filter(data[i:i+1], **params)
                else:
                    continue
                
                # 计算评分 (排除时间位置特征)
                signal_features = data.shape[2] - 1  # 排除最后一个时间位置特征
                
                snr_scores = []
                smoothness_scores = []
                
                for j in range(signal_features):
                    snr = compute_snr(data[i, :, j], candidate[0, :, j])
                    smoothness = compute_smoothness(candidate[0, :, j:j+1])
                    
                    if not np.isnan(snr) and not np.isinf(snr):
                        snr_scores.append(snr)
                    if not np.isnan(smoothness) and not np.isinf(smoothness):
                        smoothness_scores.append(smoothness)
                
                if snr_scores and smoothness_scores:
                    avg_snr = np.mean(snr_scores)
                    avg_smoothness = np.mean(smoothness_scores)
                    score = weights['snr'] * avg_snr + weights['smoothness'] * avg_smoothness
                    
                    if score > best_score:
                        best_score = score
                        best_method_data = candidate
                        
            except Exception as e:
                continue
        
        # 如果没有找到合适的方法，使用备用方法
        if best_method_data is None:
            if fallback_method == 'moving_average':
                best_method_data = denoise_with_moving_average(data[i:i+1], window_size=5)
            elif fallback_method == 'gaussian':
                best_method_data = denoise_with_gaussian_filter(data[i:i+1], sigma=1.0)
            else:
                best_method_data = data[i:i+1]  # 原始数据作为最后备用
        
        # 修复：确保去噪结果不包含NaN
        denoised_sample = best_method_data[0]
        if np.isnan(denoised_sample).any() or np.isinf(denoised_sample).any():
            denoised_sample = data[i]  # 回退到原始数据
        
        best_denoised[i] = denoised_sample
    
    # 🔧 应用去噪一致性检查和增强
    enhanced_denoised = ensure_consistent_denoising_effect(data, best_denoised)

    return enhanced_denoised


def ensure_consistent_denoising_effect(original_data, denoised_data, min_effect=0.01):
    """
    🔧 确保一致的去噪效果 - 防止某些样本去噪无效

    Args:
        original_data: 原始数据
        denoised_data: 去噪后数据
        min_effect: 最小MSE差异阈值

    Returns:
        enhanced_denoised_data: 确保有效果的去噪数据
    """
    enhanced_denoised = denoised_data.copy()

    # 检查每个样本的去噪效果
    for i in range(original_data.shape[0]):
        # 计算去噪效果 (排除时间位置特征)
        signal_features = original_data.shape[2] - 1
        orig_sample = original_data[i, :, :signal_features]
        denoised_sample = denoised_data[i, :, :signal_features]

        mse_diff = np.mean((orig_sample - denoised_sample) ** 2)

        # 如果去噪效果不足，应用额外的去噪
        if mse_diff < min_effect:
            # 应用更强的去噪
            enhanced_sample = apply_enhanced_denoising_single_sample(orig_sample)
            enhanced_denoised[i, :, :signal_features] = enhanced_sample

            # 验证增强后的效果
            new_mse = np.mean((orig_sample - enhanced_sample) ** 2)
            if new_mse < min_effect:
                # 如果仍然不足，添加轻微的平滑
                enhanced_denoised[i, :, :signal_features] = apply_forced_smoothing(
                    orig_sample, target_mse=min_effect
                )

    return enhanced_denoised


def apply_enhanced_denoising_single_sample(signal_data):
    """应用增强去噪 - 单个样本的多种方法组合"""
    # 添加batch维度以使用现有函数
    data_with_batch = signal_data[np.newaxis, :, :]

    # 方法1: 更强的移动平均
    try:
        denoised1 = denoise_with_moving_average(data_with_batch, window_size=7)[0]
    except:
        denoised1 = signal_data

    # 方法2: 高斯滤波
    try:
        denoised2 = denoise_with_gaussian_filter(data_with_batch, sigma=1.5)[0]
    except:
        denoised2 = signal_data

    # 方法3: Savitzky-Golay滤波
    try:
        denoised3 = denoise_with_savgol_filter(data_with_batch, window_length=7, polyorder=2)[0]
    except:
        denoised3 = signal_data

    # 加权组合
    enhanced = 0.4 * denoised1 + 0.3 * denoised2 + 0.3 * denoised3

    return enhanced


def apply_forced_smoothing(signal_data, target_mse=0.01):
    """强制平滑以达到目标MSE"""
    try:
        from scipy.ndimage import gaussian_filter1d

        # 逐渐增加平滑强度直到达到目标MSE
        for sigma in [0.5, 1.0, 1.5, 2.0, 2.5]:
            smoothed = signal_data.copy()
            for feature_idx in range(signal_data.shape[1]):
                smoothed[:, feature_idx] = gaussian_filter1d(
                    signal_data[:, feature_idx], sigma=sigma
                )

            mse = np.mean((signal_data - smoothed) ** 2)
            if mse >= target_mse:
                return smoothed

        # 如果仍然不够，添加轻微噪声扰动
        noise_scale = np.sqrt(target_mse) * 0.5
        noise = np.random.normal(0, noise_scale, signal_data.shape)
        return smoothed + noise

    except ImportError:
        # 如果scipy不可用，使用简单的移动平均
        window_size = 5
        smoothed = signal_data.copy()
        for feature_idx in range(signal_data.shape[1]):
            padded = np.pad(signal_data[:, feature_idx], (window_size//2, window_size//2), mode='edge')
            smoothed[:, feature_idx] = np.convolve(padded, np.ones(window_size)/window_size, mode='valid')
        return smoothed

# 更新 apply_denoising 以支持自适应
def apply_denoising(data, method='adaptive', **kwargs):
    if method == 'adaptive':
        return adaptive_denoise(data, **kwargs)
    elif method == 'moving_average':
        return denoise_with_moving_average(data, **kwargs)
    elif method == 'gaussian':
        return denoise_with_gaussian_filter(data, **kwargs)
    elif method == 'savgol':
        return denoise_with_savgol_filter(data, **kwargs)
    else:
        raise ValueError(f"Unknown denoising method: {method}")

def generate_decl_training_data(data, mask, noise_amplification_factor=2.0):
    """
    为DECL训练生成三元组数据: (增噪样本, 原始样本, 去噪样本)
    
    按照论文描述实现定向噪声放大策略：
    1. 使用自适应去噪策略生成高质量去噪版本
    2. 计算原始样本与去噪版本的残差，分离出最具干扰性的特定噪声分量
    3. 对特定噪声分量进行定向放大并重新叠加到原始样本上
    4. 生成包含清晰噪声递减梯度的三元组
    
    Args:
        data (np.ndarray): 原始数据 (n_samples, n_timesteps, n_features)
        mask (np.ndarray): 掩码数据
        noise_amplification_factor (float): 调节噪声放大强度的超参数 α
        
    Returns:
        dict: 包含 'x', 'x_denoised', 'x_noisy', 'mask' 的字典
              其中 x_noisy 是增噪样本，x 是原始样本，x_denoised 是去噪样本
    """
    # 1. 原始数据
    x_orig = data.copy()
    
    # 2. 去噪数据 - 使用自适应去噪策略生成高质量去噪版本（正样本）
    x_denoised = adaptive_denoise(data)
    
    # 修复：确保去噪数据不包含NaN或无穷值
    x_denoised = np.where(np.isnan(x_denoised), data, x_denoised)
    x_denoised = np.where(np.isinf(x_denoised), data, x_denoised)
    
    # 3. 定向噪声放大策略构建增噪样本
    x_noisy = data.copy()
    signal_features = data.shape[2] - 1  # 排除时间位置特征
    
    for i in range(data.shape[0]):
        for j in range(signal_features):  # 只对信号特征进行噪声放大
            # 步骤1：计算原始样本与去噪版本的残差，分离出特定噪声分量
            noise_component = x_orig[i, :, j] - x_denoised[i, :, j]
            
            # 步骤2：对特定噪声分量进行定向放大
            amplified_noise = noise_amplification_factor * noise_component
            
            # 步骤3：将放大后的噪声重新叠加到原始样本上，合成增噪样本
            x_noisy[i, :, j] = x_orig[i, :, j] + amplified_noise
    
    # 返回三元组：{增噪样本，原始样本，去噪样本}
    # 这个三元组内含清晰的噪声递减梯度，为对比学习提供结构化且高度可靠的信号
    return {
        'x': x_orig,           # 原始样本（中等噪声水平）
        'x_denoised': x_denoised,  # 去噪样本（低噪声水平，正样本）
        'x_noisy': x_noisy,    # 增噪样本（高噪声水平，负样本）
        'mask': mask
    }

def denoise_with_moving_average_optimized(data, window_size=5):
    """
    优化版本的移动平均去噪
    """
    return denoise_with_moving_average(data, window_size)

def denoise_with_gaussian_filter_optimized(data, sigma=1.0):
    """
    优化版本的高斯滤波去噪
    """
    return denoise_with_gaussian_filter(data, sigma)


def validate_triplet_data_quality(x_orig, x_denoised, x_noisy,
                                 min_denoising_effect=0.005,
                                 min_noise_effect=0.01,
                                 similarity_threshold=0.05):
    """
    🔍 验证三元组数据质量 - 确保对比学习的有效性

    Args:
        x_orig: 原始数据
        x_denoised: 去噪数据
        x_noisy: 增噪数据
        min_denoising_effect: 最小去噪效果阈值
        min_noise_effect: 最小噪声效果阈值
        similarity_threshold: 相似度差异阈值

    Returns:
        tuple: (质量是否合格, 质量报告)
    """
    issues = []

    # 排除时间位置特征
    signal_features = x_orig.shape[2] - 1

    # 1. 检查去噪效果
    denoising_effects = []
    for i in range(x_orig.shape[0]):
        orig_sample = x_orig[i, :, :signal_features]
        denoised_sample = x_denoised[i, :, :signal_features]
        mse_diff = np.mean((orig_sample - denoised_sample) ** 2)
        denoising_effects.append(mse_diff)

    avg_denoising_effect = np.mean(denoising_effects)
    if avg_denoising_effect < min_denoising_effect:
        issues.append(f"去噪效果不足: {avg_denoising_effect:.6f} < {min_denoising_effect}")

    # 2. 检查噪声效果
    noise_effects = []
    for i in range(x_orig.shape[0]):
        orig_sample = x_orig[i, :, :signal_features]
        noisy_sample = x_noisy[i, :, :signal_features]
        mse_diff = np.mean((orig_sample - noisy_sample) ** 2)
        noise_effects.append(mse_diff)

    avg_noise_effect = np.mean(noise_effects)
    if avg_noise_effect < min_noise_effect:
        issues.append(f"噪声效果不足: {avg_noise_effect:.6f} < {min_noise_effect}")

    # 3. 检查相似度关系 (使用简化的余弦相似度)
    pos_similarities = []  # 原始-去噪相似度
    neg_similarities = []  # 原始-增噪相似度

    for i in range(min(10, x_orig.shape[0])):  # 只检查前10个样本以节省时间
        orig_flat = x_orig[i, :, :signal_features].flatten()
        denoised_flat = x_denoised[i, :, :signal_features].flatten()
        noisy_flat = x_noisy[i, :, :signal_features].flatten()

        # 计算余弦相似度
        pos_sim = np.dot(orig_flat, denoised_flat) / (
            np.linalg.norm(orig_flat) * np.linalg.norm(denoised_flat) + 1e-8
        )
        neg_sim = np.dot(orig_flat, noisy_flat) / (
            np.linalg.norm(orig_flat) * np.linalg.norm(noisy_flat) + 1e-8
        )

        pos_similarities.append(pos_sim)
        neg_similarities.append(neg_sim)

    avg_pos_sim = np.mean(pos_similarities)
    avg_neg_sim = np.mean(neg_similarities)
    similarity_gap = avg_pos_sim - avg_neg_sim

    if similarity_gap < similarity_threshold:
        issues.append(f"相似度关系错误: pos_sim={avg_pos_sim:.4f}, neg_sim={avg_neg_sim:.4f}, gap={similarity_gap:.4f}")

    # 4. 检查数据完整性
    if np.isnan(x_denoised).any() or np.isinf(x_denoised).any():
        issues.append("去噪数据包含NaN或Inf")

    if np.isnan(x_noisy).any() or np.isinf(x_noisy).any():
        issues.append("增噪数据包含NaN或Inf")

    # 生成质量报告
    quality_ok = len(issues) == 0
    quality_report = "; ".join(issues) if issues else "所有检查通过"

    return quality_ok, quality_report


def generate_decl_training_data_with_validation(data, mask, noise_amplification_factor=2.0,
                                               validate_quality=True, max_retries=3):
    """
    🚀 带质量验证的DECL训练数据生成

    Args:
        data: 原始数据 (n_samples, n_timesteps, n_features)
        mask: 掩码数据
        noise_amplification_factor: 噪声放大因子
        validate_quality: 是否验证数据质量
        max_retries: 最大重试次数

    Returns:
        dict: 包含 'x', 'x_denoised', 'x_noisy', 'mask' 的字典
    """
    print(f"🔄 生成DECL训练数据 (带质量验证)...")
    print(f"   📊 输入数据形状: {data.shape}")
    print(f"   🔧 噪声放大因子: {noise_amplification_factor}")

    for attempt in range(max_retries):
        print(f"   🎯 尝试 {attempt + 1}/{max_retries}")

        # 生成数据
        result = generate_decl_training_data(data, mask, noise_amplification_factor)

        # 质量验证
        if validate_quality:
            quality_ok, quality_report = validate_triplet_data_quality(
                result['x'], result['x_denoised'], result['x_noisy']
            )

            if quality_ok:
                print("   ✅ 数据质量验证通过")
                return result
            else:
                print(f"   ⚠️ 数据质量验证失败 (尝试 {attempt + 1}/{max_retries})")
                print(f"      问题: {quality_report}")

                if attempt < max_retries - 1:
                    # 调整参数重试
                    noise_amplification_factor *= 1.5  # 增加噪声放大
                    print(f"      🔧 调整噪声放大因子至: {noise_amplification_factor}")
                else:
                    print("   ❌ 达到最大重试次数，使用当前结果")
                    return result
        else:
            return result

    return result
