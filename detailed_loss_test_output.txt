Dataset: ConstPos
Arguments: Namespace(batch_size=8, dataset='ConstPos', epochs=3, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.1, lambda_decl=0.1, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.001, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, patience=10, precision_target=0.65, recall_target=0.75, reconstruct_target='original', repr_dims=320, run_name='detailed_loss_test', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=1.0, use_focal_loss=False)
✅ 已设置随机种子: 42
Loading data... done
(480, 50, 37)

🔍 === TRAINING FLOW INVESTIGATION ===
📊 Input train_data type: <class 'dict'>
📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])
🎯 DECL Mode Detected: True
🔄 === DECL TRAINING BRANCH ===
📈 Original data shape: (480, 50, 37)
📈 Denoised data shape: (480, 50, 37)
📈 Noisy data shape: (480, 50, 37)
📈 Mask shape: (480, 50, 36)
📈 Batch size: 8
🎯 初始化Gumbel-Softmax门控网络...
🚀 初始化基于Gumbel-Softmax的自适应去噪系统...
✅ 自适应去噪模块已创建
   - 输入维度: 36
   - 去噪器数量: 7
   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']
✅ 门控网络初始化完成
🌡️ 温度退火设置: 初始2.0 -> 最终0.1
📊 DataLoader created with 60 batches
📊 Expected batches per epoch: 60
🔧 DECL Optimizer created with 64 parameters (包含门控网络)

🎯 === TRAINING PARAMETERS INVESTIGATION ===
📊 Input n_epochs: 3
📊 Input n_iters: None
📊 Final training parameters:
   - n_epochs: 3
   - n_iters: None
   - max_epochs: 3
   - batches_per_epoch: 60
   - self.n_epochs: 0
   - self.n_iters: 0

🚀 === STARTING TRAINING LOOP ===

📊 === 损失函数配置 ===
   🔧 重构损失权重 (w_rec): 1.0
   🔧 三元组损失权重 (w_triplet): 0.1
   ✅ 分离损失权重 (w_sep): 已完全移除
   📝 总损失公式: Total = 1.0 * Reconstruction + 0.1 * Triplet
   🌡️ 对比温度 (temp_contrast): 1.0
   🎯 方向对齐权重 (lambda_align): 0.1

📅 Starting Epoch 0
🔍 Check termination: n_epochs=3, self.n_epochs=0
🔄 === DECL TRAINING LOOP (Epoch 0) ===
   🔍 === 损失组件验证 ===
      ✅ 重构损失: 启用 (权重=1.0)
      ✅ 三元组损失: 启用 (权重=0.1)
      ❌ 分离损失: 已移除 (不存在w_sep属性)
      📝 预期损失计算: loss = 1.0 * loss_rec + 0.1 * loss_triplet
      ✅ 确认: w_sep属性已完全移除
   📊 Batch 5: Total=0.9193 (Rec=0.8586, Trip=0.6071)
   📊 === 详细损失分解 (Batch 10/60) ===
      🔧 重构损失 (Reconstruction): 0.881891
      🔧 三元组损失 (Triplet): 0.532204
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.881891 = 0.881891
      📝 加权三元组损失: 0.100 × 0.532204 = 0.053220
      🎯 总损失 (Total): 0.935111
      ✅ 验证公式: 0.935111 = 0.935111
   📊 Batch 15: Total=1.3828 (Rec=1.3329, Trip=0.4992)
🔄 Batch 20/60
   📊 === 详细损失分解 (Batch 20/60) ===
      🔧 重构损失 (Reconstruction): 0.943121
      🔧 三元组损失 (Triplet): 0.503252
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.943121 = 0.943121
      📝 加权三元组损失: 0.100 × 0.503252 = 0.050325
      🎯 总损失 (Total): 0.993446
      ✅ 验证公式: 0.993446 = 0.993446
   📊 Batch 25: Total=0.9973 (Rec=0.9509, Trip=0.4644)
   📊 === 详细损失分解 (Batch 30/60) ===
      🔧 重构损失 (Reconstruction): 0.684628
      🔧 三元组损失 (Triplet): 0.403040
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.684628 = 0.684628
      📝 加权三元组损失: 0.100 × 0.403040 = 0.040304
      🎯 总损失 (Total): 0.724932
      ✅ 验证公式: 0.724932 = 0.724932
   📊 Batch 35: Total=1.5266 (Rec=1.4819, Trip=0.4471)
🔄 Batch 40/60
   📊 === 详细损失分解 (Batch 40/60) ===
      🔧 重构损失 (Reconstruction): 0.827336
      🔧 三元组损失 (Triplet): 0.421162
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.827336 = 0.827336
      📝 加权三元组损失: 0.100 × 0.421162 = 0.042116
      🎯 总损失 (Total): 0.869452
      ✅ 验证公式: 0.869452 = 0.869452
   📊 Batch 40: Temp=2.000, AvgLoss=0.9671
   📊 Batch 45: Total=1.0865 (Rec=1.0434, Trip=0.4308)
   📊 === 详细损失分解 (Batch 50/60) ===
      🔧 重构损失 (Reconstruction): 0.723443
      🔧 三元组损失 (Triplet): 0.425099
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.723443 = 0.723443
      📝 加权三元组损失: 0.100 × 0.425099 = 0.042510
      🎯 总损失 (Total): 0.765953
      ✅ 验证公式: 0.765953 = 0.765953
   📊 Batch 55: Total=0.9144 (Rec=0.8765, Trip=0.3783)
🔄 Batch 60/60
   📊 === 详细损失分解 (Batch 60/60) ===
      🔧 重构损失 (Reconstruction): 0.736838
      🔧 三元组损失 (Triplet): 0.389805
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.736838 = 0.736838
      📝 加权三元组损失: 0.100 × 0.389805 = 0.038981
      🎯 总损失 (Total): 0.775819
      ✅ 验证公式: 0.775819 = 0.775819
📊 DECL Epoch 0 completed: 60 batches processed

📊 === EPOCH 0 COMPLETED ===
   ⏱️ Epoch time: 5.092s
   ⏱️ Total time so far: 5.092s
   📈 Batches processed: 60
   📈 Average total loss: 0.944287
   📈 Total iterations so far: 60
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #0: loss=0.9442869414885838
   📈 Incremented self.n_epochs to: 1

📅 Starting Epoch 1
🔍 Check termination: n_epochs=3, self.n_epochs=1
🔄 === DECL TRAINING LOOP (Epoch 1) ===
   📊 Batch 5: Total=0.7336 (Rec=0.6975, Trip=0.3613)
   📊 === 详细损失分解 (Batch 10/60) ===
      🔧 重构损失 (Reconstruction): 0.778117
      🔧 三元组损失 (Triplet): 0.328024
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.778117 = 0.778117
      📝 加权三元组损失: 0.100 × 0.328024 = 0.032802
      🎯 总损失 (Total): 0.810919
      ✅ 验证公式: 0.810919 = 0.810919
   📊 Batch 15: Total=0.8438 (Rec=0.8137, Trip=0.3008)
🔄 Batch 20/60
   📊 === 详细损失分解 (Batch 20/60) ===
      🔧 重构损失 (Reconstruction): 0.837240
      🔧 三元组损失 (Triplet): 0.317626
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.837240 = 0.837240
      📝 加权三元组损失: 0.100 × 0.317626 = 0.031763
      🎯 总损失 (Total): 0.869002
      ✅ 验证公式: 0.869002 = 0.869002
   📊 Batch 25: Total=0.7580 (Rec=0.7334, Trip=0.2463)
   📊 === 详细损失分解 (Batch 30/60) ===
      🔧 重构损失 (Reconstruction): 0.801373
      🔧 三元组损失 (Triplet): 0.251178
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.801373 = 0.801373
      📝 加权三元组损失: 0.100 × 0.251178 = 0.025118
      🎯 总损失 (Total): 0.826491
      ✅ 验证公式: 0.826491 = 0.826491
   📊 Batch 35: Total=0.9897 (Rec=0.9681, Trip=0.2153)
🔄 Batch 40/60
   📊 === 详细损失分解 (Batch 40/60) ===
      🔧 重构损失 (Reconstruction): 0.786653
      🔧 三元组损失 (Triplet): 0.200371
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.786653 = 0.786653
      📝 加权三元组损失: 0.100 × 0.200371 = 0.020037
      🎯 总损失 (Total): 0.806690
      ✅ 验证公式: 0.806690 = 0.806690
   📊 Batch 40: Temp=0.737, AvgLoss=0.9177
   📊 Batch 45: Total=0.7552 (Rec=0.7365, Trip=0.1869)
   📊 === 详细损失分解 (Batch 50/60) ===
      🔧 重构损失 (Reconstruction): 0.730589
      🔧 三元组损失 (Triplet): 0.199266
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.730589 = 0.730589
      📝 加权三元组损失: 0.100 × 0.199266 = 0.019927
      🎯 总损失 (Total): 0.750515
      ✅ 验证公式: 0.750515 = 0.750515
   📊 Batch 55: Total=0.9640 (Rec=0.9458, Trip=0.1812)
🔄 Batch 60/60
   📊 === 详细损失分解 (Batch 60/60) ===
      🔧 重构损失 (Reconstruction): 0.939533
      🔧 三元组损失 (Triplet): 0.178588
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.939533 = 0.939533
      📝 加权三元组损失: 0.100 × 0.178588 = 0.017859
      🎯 总损失 (Total): 0.957392
      ✅ 验证公式: 0.957392 = 0.957392
📊 DECL Epoch 1 completed: 60 batches processed

📊 === EPOCH 1 COMPLETED ===
   ⏱️ Epoch time: 4.388s
   ⏱️ Total time so far: 9.480s
   📈 Batches processed: 60
   📈 Average total loss: 0.892639
   📈 Total iterations so far: 120
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #1: loss=0.8926393528779347
   📈 Incremented self.n_epochs to: 2

📅 Starting Epoch 2
🔍 Check termination: n_epochs=3, self.n_epochs=2
🔄 === DECL TRAINING LOOP (Epoch 2) ===
   📊 Batch 5: Total=0.8254 (Rec=0.8072, Trip=0.1826)
   📊 === 详细损失分解 (Batch 10/60) ===
      🔧 重构损失 (Reconstruction): 0.895476
      🔧 三元组损失 (Triplet): 0.192217
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.895476 = 0.895476
      📝 加权三元组损失: 0.100 × 0.192217 = 0.019222
      🎯 总损失 (Total): 0.914697
      ✅ 验证公式: 0.914697 = 0.914697
   📊 Batch 15: Total=0.7787 (Rec=0.7607, Trip=0.1795)
🔄 Batch 20/60
   📊 === 详细损失分解 (Batch 20/60) ===
      🔧 重构损失 (Reconstruction): 0.810693
      🔧 三元组损失 (Triplet): 0.175865
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.810693 = 0.810693
      📝 加权三元组损失: 0.100 × 0.175865 = 0.017586
      🎯 总损失 (Total): 0.828279
      ✅ 验证公式: 0.828279 = 0.828279
   📊 Batch 25: Total=0.6524 (Rec=0.6351, Trip=0.1732)
   📊 === 详细损失分解 (Batch 30/60) ===
      🔧 重构损失 (Reconstruction): 0.745290
      🔧 三元组损失 (Triplet): 0.176387
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.745290 = 0.745290
      📝 加权三元组损失: 0.100 × 0.176387 = 0.017639
      🎯 总损失 (Total): 0.762929
      ✅ 验证公式: 0.762929 = 0.762929
   📊 Batch 35: Total=1.0243 (Rec=1.0058, Trip=0.1849)
🔄 Batch 40/60
   📊 === 详细损失分解 (Batch 40/60) ===
      🔧 重构损失 (Reconstruction): 0.775383
      🔧 三元组损失 (Triplet): 0.180878
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.775383 = 0.775383
      📝 加权三元组损失: 0.100 × 0.180878 = 0.018088
      🎯 总损失 (Total): 0.793471
      ✅ 验证公式: 0.793471 = 0.793471
   📊 Batch 40: Temp=0.271, AvgLoss=0.8872
   📊 Batch 45: Total=0.6589 (Rec=0.6416, Trip=0.1734)
   📊 === 详细损失分解 (Batch 50/60) ===
      🔧 重构损失 (Reconstruction): 0.728474
      🔧 三元组损失 (Triplet): 0.171283
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.728474 = 0.728474
      📝 加权三元组损失: 0.100 × 0.171283 = 0.017128
      🎯 总损失 (Total): 0.745602
      ✅ 验证公式: 0.745602 = 0.745602
   📊 Batch 55: Total=0.8842 (Rec=0.8668, Trip=0.1742)
🔄 Batch 60/60
   📊 === 详细损失分解 (Batch 60/60) ===
      🔧 重构损失 (Reconstruction): 0.716804
      🔧 三元组损失 (Triplet): 0.171990
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.716804 = 0.716804
      📝 加权三元组损失: 0.100 × 0.171990 = 0.017199
      🎯 总损失 (Total): 0.734003
      ✅ 验证公式: 0.734003 = 0.734003
📊 DECL Epoch 2 completed: 60 batches processed

📊 === EPOCH 2 COMPLETED ===
   ⏱️ Epoch time: 5.095s
   ⏱️ Total time so far: 14.576s
   📈 Batches processed: 60
   📈 Average total loss: 0.952788
   📈 Total iterations so far: 180
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      ✅ 分离损失状态: 已完全移除
      🔧 损失权重: w_rec=1.0, w_triplet=0.1
      📝 损失公式: Total = 1.0 × Reconstruction + 0.1 × Triplet
Epoch #2: loss=0.952787705262502
   📈 Incremented self.n_epochs to: 3

📅 Starting Epoch 3
🔍 Check termination: n_epochs=3, self.n_epochs=3
🛑 Breaking due to epoch limit: 3 >= 3

🏁 === TRAINING COMPLETED ===
   ⏱️ Total training time: 14.576s
   📈 Total epochs completed: 3
   📈 Total iterations completed: 180
   📈 Final loss: 0.952787705262502
   📊 Loss history: [0.9442869414885838, 0.8926393528779347, 0.952787705262502]

Training time: 0:00:15.060763

Evaluation result: {'f1': 0.46468401486988853, 'precision': 0.3443526170798898, 'recall': 0.7142857142857143, 'infer_time': 25.068958520889282}
Finished.
