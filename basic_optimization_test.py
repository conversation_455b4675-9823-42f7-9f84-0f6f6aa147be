#!/usr/bin/env python3
"""
🧪 基础优化测试
只测试基本的损失权重优化，不使用可能有问题的信号处理组件
"""

import subprocess
import json
import re
import sys
import os
from datetime import datetime

def run_basic_optimization_test(reconstruct_target, description):
    """运行基础优化测试"""
    
    print(f"\n🧪 运行基础优化测试: {description}")
    print(f"🎯 重建目标: {reconstruct_target}")
    print(f"🔧 优化措施: 损失权重重新平衡 + 对比学习参数调优")
    
    cmd = [
        "python", "src/train.py", 
        "ConstPos", f"basic_opt_{reconstruct_target}",
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", "15",  # 减少训练轮数以快速测试
        "--batch-size", "16",
        "--lr", "0.001",  # 使用标准学习率
        "--gpu", "0",
        "--reconstruct_target", reconstruct_target,
        # 🎯 基础优化参数（只使用确定有效的参数）
        "--lambda_decl", "0.3",  # 降低对比损失权重（原来0.5）
        "--temp_contrast", "0.5",  # 降低对比温度（原来1.0）
        "--lambda_align", "0.05"  # 降低方向对齐权重（原来0.1）
    ]
    
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10分钟超时
        
        if result.returncode != 0:
            print(f"❌ 测试失败:")
            print(f"   stderr: {result.stderr}")
            return {
                "reconstruct_target": reconstruct_target,
                "description": description,
                "success": False,
                "error": result.stderr,
                "f1_score": 0.0,
                "precision": 0.0,
                "recall": 0.0,
                "training_stable": False,
                "loss_info": "❌ 训练失败"
            }
        
        output = result.stdout
        print(f"✅ 测试完成，解析结果...")
        
        # 解析性能指标
        f1_match = re.search(r"f1[:\s]+([0-9.]+)", output, re.IGNORECASE)
        precision_match = re.search(r"precision[:\s]+([0-9.]+)", output, re.IGNORECASE)
        recall_match = re.search(r"recall[:\s]+([0-9.]+)", output, re.IGNORECASE)
        
        f1_score = float(f1_match.group(1)) if f1_match else 0.0
        precision = float(precision_match.group(1)) if precision_match else 0.0
        recall = float(recall_match.group(1)) if recall_match else 0.0
        
        # 检查训练稳定性
        nan_pattern = r"(nan|NaN|inf|Inf)"
        training_stable = not bool(re.search(nan_pattern, output))
        
        # 解析损失信息
        loss_info = "✅ 训练正常"
        loss_matches = re.findall(r"loss[:\s]*([0-9.]+)", output, re.IGNORECASE)
        if len(loss_matches) >= 2:
            initial_loss = float(loss_matches[0])
            final_loss = float(loss_matches[-1])
            improvement = initial_loss - final_loss
            loss_info = f"✅ 训练正常：{initial_loss:.4f} → {final_loss:.4f} (改进{improvement:.4f})"
        
        print(f"📊 结果解析完成:")
        print(f"   F1: {f1_score:.4f}")
        print(f"   Precision: {precision:.4f}")
        print(f"   Recall: {recall:.4f}")
        print(f"   训练稳定: {training_stable}")
        
        return {
            "reconstruct_target": reconstruct_target,
            "description": description,
            "f1_score": f1_score,
            "precision": precision,
            "recall": recall,
            "training_stable": training_stable,
            "loss_info": loss_info,
            "success": True
        }
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 (10分钟)")
        return {
            "reconstruct_target": reconstruct_target,
            "description": description,
            "success": False,
            "error": "测试超时",
            "f1_score": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "training_stable": False,
            "loss_info": "⏰ 测试超时"
        }
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {
            "reconstruct_target": reconstruct_target,
            "description": description,
            "success": False,
            "error": str(e),
            "f1_score": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "training_stable": False,
            "loss_info": f"❌ 测试异常: {e}"
        }

def main():
    """主测试流程"""
    print("🧪 === 基础优化测试 ===")
    print("🎯 目标: 测试基本的损失权重优化是否有效")
    print("🔧 优化措施:")
    print("   1. 降低对比损失权重 (0.5 → 0.3)")
    print("   2. 降低对比温度 (1.0 → 0.5)")
    print("   3. 降低方向对齐权重 (0.1 → 0.05)")
    print("   4. 不使用可能有问题的信号处理组件")
    
    # 实验配置
    experiments = [
        ("original", "方案A: 重建原始信号 (基础优化)"),
        ("denoised", "方案B: 重建去噪信号 (基础优化)")
    ]
    
    results = []
    
    for reconstruct_target, description in experiments:
        result = run_basic_optimization_test(reconstruct_target, description)
        results.append(result)
        
        # 实时显示结果
        print(f"\n📊 {description} 结果:")
        if result["success"]:
            print(f"   ✅ F1分数: {result['f1_score']:.4f}")
            print(f"   ✅ 精度: {result['precision']:.4f}")
            print(f"   ✅ 召回率: {result['recall']:.4f}")
            print(f"   ✅ 训练稳定: {result['training_stable']}")
        else:
            print(f"   ❌ 测试失败: {result.get('error', '未知错误')}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"basic_optimization_test_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 结果已保存到: {filename}")
    
    # 性能对比分析
    print(f"\n📈 === 性能对比分析 ===")
    
    if all(r["success"] for r in results):
        original_result = results[0]
        denoised_result = results[1]
        
        print(f"方案A (原始信号):")
        print(f"   F1: {original_result['f1_score']:.4f}")
        print(f"   精度: {original_result['precision']:.4f}")
        print(f"   召回率: {original_result['recall']:.4f}")
        
        print(f"方案B (去噪信号):")
        print(f"   F1: {denoised_result['f1_score']:.4f}")
        print(f"   精度: {denoised_result['precision']:.4f}")
        print(f"   召回率: {denoised_result['recall']:.4f}")
        
        # 与基准对比（原始结果：F1=0.56, Precision=0.416, Recall=0.857）
        best_f1 = max(original_result['f1_score'], denoised_result['f1_score'])
        best_precision = max(original_result['precision'], denoised_result['precision'])
        
        print(f"\n🎯 与基准对比:")
        print(f"   基准 F1分数: 0.560")
        print(f"   当前最佳 F1: {best_f1:.4f} ({'✅ 改进' if best_f1 > 0.56 else '❌ 未改进'})")
        print(f"   基准精度: 0.416")
        print(f"   当前最佳精度: {best_precision:.4f} ({'✅ 改进' if best_precision > 0.416 else '❌ 未改进'})")
        
        if best_f1 > 0.56 and best_precision > 0.416:
            print(f"   🎉 基础优化有效！F1和精度都有改进")
        elif best_f1 > 0.56 or best_precision > 0.416:
            print(f"   ⚠️ 部分改进：{'F1' if best_f1 > 0.56 else '精度'}有改进")
        else:
            print(f"   ❌ 基础优化无效，需要进一步调整")
    
    print(f"\n🏁 基础测试完成!")

if __name__ == "__main__":
    main()
