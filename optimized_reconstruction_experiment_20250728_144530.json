[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (优化版)", "success": false, "error": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\nTraceback (most recent call last):\n  File \"src/train.py\", line 181, in <module>\n    loss_log = model.fit(\n  File \"/mnt/d/WorkSpace/TimesURL/src/timesurl.py\", line 356, in fit\n    enhanced_denoised, noise_level = self.enhanced_denoiser(x_denoised_features)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1518, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1527, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/mnt/d/WorkSpace/TimesURL/src/models/signal_processing.py\", line 166, in forward\n    multi_scale_features = self.multi_scale_analyzer(filtered_signal)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1518, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1527, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/mnt/d/WorkSpace/TimesURL/src/models/signal_processing.py\", line 113, in forward\n    concatenated = torch.cat(scale_features, dim=2)  # (B, T, D*num_scales)\nRuntimeError: Sizes of tensors must match except in dimension 2. Expected size 50 but got size 46 for tensor number 1 in the list.\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}]