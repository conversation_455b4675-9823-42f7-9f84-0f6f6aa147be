Dataset: ConstPos
Arguments: Namespace(batch_size=16, dataset='ConstPos', epochs=15, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.1, lambda_decl=1.0, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.001, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, patience=10, precision_target=0.65, recall_target=0.75, reconstruct_target='original', repr_dims=320, run_name='baseline_test_15epochs', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=0.5, use_focal_loss=False)
✅ 已设置随机种子: 42
Loading data... done
(480, 50, 37)

🔍 === TRAINING FLOW INVESTIGATION ===
📊 Input train_data type: <class 'dict'>
📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])
🎯 DECL Mode Detected: True
🔄 === DECL TRAINING BRANCH ===
📈 Original data shape: (480, 50, 37)
📈 Denoised data shape: (480, 50, 37)
📈 Noisy data shape: (480, 50, 37)
📈 Mask shape: (480, 50, 36)
📈 Batch size: 16
🎯 初始化Gumbel-Softmax门控网络...
🚀 初始化基于Gumbel-Softmax的自适应去噪系统...
✅ 自适应去噪模块已创建
   - 输入维度: 36
   - 去噪器数量: 7
   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']
✅ 门控网络初始化完成
🌡️ 温度退火设置: 初始2.0 -> 最终0.1
📊 DataLoader created with 30 batches
📊 Expected batches per epoch: 30
🔧 DECL Optimizer created with 64 parameters (包含门控网络)

🎯 === TRAINING PARAMETERS INVESTIGATION ===
📊 Input n_epochs: 15
📊 Input n_iters: None
📊 Final training parameters:
   - n_epochs: 15
   - n_iters: None
   - max_epochs: 15
   - batches_per_epoch: 30
   - self.n_epochs: 0
   - self.n_iters: 0

🚀 === STARTING TRAINING LOOP ===

📊 === 损失函数配置 ===
   🔧 重构损失权重 (w_rec): 1.0
   🔧 三元组损失权重 (w_triplet): 1.0
   ✅ 分离损失权重 (w_sep): 已完全移除
   📝 总损失公式: Total = 1.0 * Reconstruction + 1.0 * Triplet
   🌡️ 对比温度 (temp_contrast): 0.5
   🎯 方向对齐权重 (lambda_align): 0.1

📅 Starting Epoch 0
🔍 Check termination: n_epochs=15, self.n_epochs=0
🔄 === DECL TRAINING LOOP (Epoch 0) ===
   🔍 === 损失组件验证 ===
      ✅ 重构损失: 启用 (权重=1.0)
      ✅ 三元组损失: 启用 (权重=1.0)
      ❌ 分离损失: 已移除 (不存在w_sep属性)
      📝 预期损失计算: loss = 1.0 * loss_rec + 1.0 * loss_triplet
      ✅ 确认: w_sep属性已完全移除
   📊 Batch 5: Total=1.6502 (Rec=1.2619, Trip=0.3883)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.996478
      🔧 三元组损失 (Triplet): 0.216660
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.996478 = 0.996478
      📝 加权三元组损失: 1.000 × 0.216660 = 0.216660
      🎯 总损失 (Total): 1.213138
      ✅ 验证公式: 1.213138 = 1.213138
   📊 Batch 15: Total=0.8052 (Rec=0.7503, Trip=0.0549)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.885406
      🔧 三元组损失 (Triplet): 0.083120
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.885406 = 1.885406
      📝 加权三元组损失: 1.000 × 0.083120 = 0.083120
      🎯 总损失 (Total): 1.968526
      ✅ 验证公式: 1.968526 = 1.968526
   📊 Batch 25: Total=0.9375 (Rec=0.9026, Trip=0.0349)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.869418
      🔧 三元组损失 (Triplet): 0.033480
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.869418 = 0.869418
      📝 加权三元组损失: 1.000 × 0.033480 = 0.033480
      🎯 总损失 (Total): 0.902898
      ✅ 验证公式: 0.902898 = 0.902898
📊 DECL Epoch 0 completed: 30 batches processed

📊 === EPOCH 0 COMPLETED ===
   ⏱️ Epoch time: 3.185s
   ⏱️ Total time so far: 3.185s
   📈 Batches processed: 30
   📈 Average total loss: 1.155996
   📈 Total iterations so far: 30
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #0: loss=1.1559963166713714
   📈 Incremented self.n_epochs to: 1

📅 Starting Epoch 1
🔍 Check termination: n_epochs=15, self.n_epochs=1
🔄 === DECL TRAINING LOOP (Epoch 1) ===
   📊 Batch 5: Total=0.8355 (Rec=0.8020, Trip=0.0335)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 1.087476
      🔧 三元组损失 (Triplet): 0.054890
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.087476 = 1.087476
      📝 加权三元组损失: 1.000 × 0.054890 = 0.054890
      🎯 总损失 (Total): 1.142366
      ✅ 验证公式: 1.142366 = 1.142366
   📊 Batch 15: Total=1.1075 (Rec=1.0370, Trip=0.0705)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.769265
      🔧 三元组损失 (Triplet): 0.037449
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.769265 = 0.769265
      📝 加权三元组损失: 1.000 × 0.037449 = 0.037449
      🎯 总损失 (Total): 0.806714
      ✅ 验证公式: 0.806714 = 0.806714
   📊 Batch 25: Total=0.9979 (Rec=0.9584, Trip=0.0395)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.043326
      🔧 三元组损失 (Triplet): 0.038829
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.043326 = 1.043326
      📝 加权三元组损失: 1.000 × 0.038829 = 0.038829
      🎯 总损失 (Total): 1.082155
      ✅ 验证公式: 1.082155 = 1.082155
📊 DECL Epoch 1 completed: 30 batches processed

📊 === EPOCH 1 COMPLETED ===
   ⏱️ Epoch time: 2.428s
   ⏱️ Total time so far: 5.613s
   📈 Batches processed: 30
   📈 Average total loss: 1.116554
   📈 Total iterations so far: 60
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #1: loss=1.1165544867515564
   📈 Incremented self.n_epochs to: 2

📅 Starting Epoch 2
🔍 Check termination: n_epochs=15, self.n_epochs=2
🔄 === DECL TRAINING LOOP (Epoch 2) ===
   📊 Batch 5: Total=0.8935 (Rec=0.8547, Trip=0.0388)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.902395
      🔧 三元组损失 (Triplet): 0.036850
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.902395 = 0.902395
      📝 加权三元组损失: 1.000 × 0.036850 = 0.036850
      🎯 总损失 (Total): 0.939245
      ✅ 验证公式: 0.939245 = 0.939245
   📊 Batch 15: Total=0.8767 (Rec=0.8402, Trip=0.0365)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.219462
      🔧 三元组损失 (Triplet): 0.037712
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.219462 = 1.219462
      📝 加权三元组损失: 1.000 × 0.037712 = 0.037712
      🎯 总损失 (Total): 1.257174
      ✅ 验证公式: 1.257174 = 1.257174
   📊 Batch 25: Total=0.8379 (Rec=0.8029, Trip=0.0350)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.951701
      🔧 三元组损失 (Triplet): 0.033973
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.951701 = 0.951701
      📝 加权三元组损失: 1.000 × 0.033973 = 0.033973
      🎯 总损失 (Total): 0.985674
      ✅ 验证公式: 0.985674 = 0.985674
📊 DECL Epoch 2 completed: 30 batches processed

📊 === EPOCH 2 COMPLETED ===
   ⏱️ Epoch time: 2.459s
   ⏱️ Total time so far: 8.072s
   📈 Batches processed: 30
   📈 Average total loss: 0.962353
   📈 Total iterations so far: 90
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #2: loss=0.9623527328173319
   📈 Incremented self.n_epochs to: 3

📅 Starting Epoch 3
🔍 Check termination: n_epochs=15, self.n_epochs=3
🔄 === DECL TRAINING LOOP (Epoch 3) ===
   📊 Batch 5: Total=0.7653 (Rec=0.7322, Trip=0.0331)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.821532
      🔧 三元组损失 (Triplet): 0.034681
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.821532 = 0.821532
      📝 加权三元组损失: 1.000 × 0.034681 = 0.034681
      🎯 总损失 (Total): 0.856213
      ✅ 验证公式: 0.856213 = 0.856213
   📊 Batch 15: Total=0.9315 (Rec=0.8980, Trip=0.0335)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.868295
      🔧 三元组损失 (Triplet): 0.033142
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.868295 = 0.868295
      📝 加权三元组损失: 1.000 × 0.033142 = 0.033142
      🎯 总损失 (Total): 0.901438
      ✅ 验证公式: 0.901438 = 0.901438
   📊 Batch 25: Total=0.9377 (Rec=0.9049, Trip=0.0328)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.163383
      🔧 三元组损失 (Triplet): 0.033425
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.163383 = 1.163383
      📝 加权三元组损失: 1.000 × 0.033425 = 0.033425
      🎯 总损失 (Total): 1.196808
      ✅ 验证公式: 1.196808 = 1.196808
📊 DECL Epoch 3 completed: 30 batches processed

📊 === EPOCH 3 COMPLETED ===
   ⏱️ Epoch time: 2.545s
   ⏱️ Total time so far: 10.618s
   📈 Batches processed: 30
   📈 Average total loss: 0.997952
   📈 Total iterations so far: 120
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #3: loss=0.9979515333970388
   📈 Incremented self.n_epochs to: 4

📅 Starting Epoch 4
🔍 Check termination: n_epochs=15, self.n_epochs=4
🔄 === DECL TRAINING LOOP (Epoch 4) ===
   📊 Batch 5: Total=0.9695 (Rec=0.9357, Trip=0.0338)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.891064
      🔧 三元组损失 (Triplet): 0.032694
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.891064 = 0.891064
      📝 加权三元组损失: 1.000 × 0.032694 = 0.032694
      🎯 总损失 (Total): 0.923758
      ✅ 验证公式: 0.923758 = 0.923758
   📊 Batch 15: Total=0.9641 (Rec=0.9307, Trip=0.0334)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.787701
      🔧 三元组损失 (Triplet): 0.032811
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.787701 = 0.787701
      📝 加权三元组损失: 1.000 × 0.032811 = 0.032811
      🎯 总损失 (Total): 0.820512
      ✅ 验证公式: 0.820512 = 0.820512
   📊 Batch 25: Total=0.8810 (Rec=0.8475, Trip=0.0334)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.892660
      🔧 三元组损失 (Triplet): 0.033034
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.892660 = 0.892660
      📝 加权三元组损失: 1.000 × 0.033034 = 0.033034
      🎯 总损失 (Total): 0.925695
      ✅ 验证公式: 0.925695 = 0.925695
📊 DECL Epoch 4 completed: 30 batches processed

📊 === EPOCH 4 COMPLETED ===
   ⏱️ Epoch time: 2.335s
   ⏱️ Total time so far: 12.953s
   📈 Batches processed: 30
   📈 Average total loss: 0.943316
   📈 Total iterations so far: 150
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #4: loss=0.9433158258597056
   📈 Incremented self.n_epochs to: 5

📅 Starting Epoch 5
🔍 Check termination: n_epochs=15, self.n_epochs=5
🔄 === DECL TRAINING LOOP (Epoch 5) ===
   📊 Batch 5: Total=0.9311 (Rec=0.8980, Trip=0.0331)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.890450
      🔧 三元组损失 (Triplet): 0.033335
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.890450 = 0.890450
      📝 加权三元组损失: 1.000 × 0.033335 = 0.033335
      🎯 总损失 (Total): 0.923785
      ✅ 验证公式: 0.923785 = 0.923785
   📊 Batch 15: Total=0.9486 (Rec=0.9149, Trip=0.0337)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.922960
      🔧 三元组损失 (Triplet): 0.034350
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.922960 = 0.922960
      📝 加权三元组损失: 1.000 × 0.034350 = 0.034350
      🎯 总损失 (Total): 0.957310
      ✅ 验证公式: 0.957310 = 0.957310
   📊 Batch 25: Total=0.8902 (Rec=0.8565, Trip=0.0337)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.774102
      🔧 三元组损失 (Triplet): 0.032560
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.774102 = 0.774102
      📝 加权三元组损失: 1.000 × 0.032560 = 0.032560
      🎯 总损失 (Total): 0.806661
      ✅ 验证公式: 0.806661 = 0.806661
📊 DECL Epoch 5 completed: 30 batches processed

📊 === EPOCH 5 COMPLETED ===
   ⏱️ Epoch time: 2.369s
   ⏱️ Total time so far: 15.322s
   📈 Batches processed: 30
   📈 Average total loss: 0.939691
   📈 Total iterations so far: 180
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #5: loss=0.9396913846333822
   📈 Incremented self.n_epochs to: 6

📅 Starting Epoch 6
🔍 Check termination: n_epochs=15, self.n_epochs=6
🔄 === DECL TRAINING LOOP (Epoch 6) ===
   📊 Batch 5: Total=0.8268 (Rec=0.7941, Trip=0.0327)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.962843
      🔧 三元组损失 (Triplet): 0.032845
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.962843 = 0.962843
      📝 加权三元组损失: 1.000 × 0.032845 = 0.032845
      🎯 总损失 (Total): 0.995688
      ✅ 验证公式: 0.995688 = 0.995688
   📊 Batch 15: Total=0.7470 (Rec=0.7142, Trip=0.0328)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 1.939501
      🔧 三元组损失 (Triplet): 0.032227
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.939501 = 1.939501
      📝 加权三元组损失: 1.000 × 0.032227 = 0.032227
      🎯 总损失 (Total): 1.971728
      ✅ 验证公式: 1.971728 = 1.971728
   📊 Batch 25: Total=0.8067 (Rec=0.7752, Trip=0.0314)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.829970
      🔧 三元组损失 (Triplet): 0.031786
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.829970 = 0.829970
      📝 加权三元组损失: 1.000 × 0.031786 = 0.031786
      🎯 总损失 (Total): 0.861756
      ✅ 验证公式: 0.861756 = 0.861756
📊 DECL Epoch 6 completed: 30 batches processed

📊 === EPOCH 6 COMPLETED ===
   ⏱️ Epoch time: 2.381s
   ⏱️ Total time so far: 17.703s
   📈 Batches processed: 30
   📈 Average total loss: 0.981612
   📈 Total iterations so far: 210
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #6: loss=0.9816121220588684
   📈 Incremented self.n_epochs to: 7

📅 Starting Epoch 7
🔍 Check termination: n_epochs=15, self.n_epochs=7
🔄 === DECL TRAINING LOOP (Epoch 7) ===
   📊 Batch 5: Total=1.0339 (Rec=1.0011, Trip=0.0328)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.894076
      🔧 三元组损失 (Triplet): 0.031784
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.894076 = 0.894076
      📝 加权三元组损失: 1.000 × 0.031784 = 0.031784
      🎯 总损失 (Total): 0.925860
      ✅ 验证公式: 0.925860 = 0.925860
   📊 Batch 15: Total=0.8678 (Rec=0.8357, Trip=0.0320)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.797056
      🔧 三元组损失 (Triplet): 0.031520
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.797056 = 0.797056
      📝 加权三元组损失: 1.000 × 0.031520 = 0.031520
      🎯 总损失 (Total): 0.828576
      ✅ 验证公式: 0.828576 = 0.828576
   📊 Batch 25: Total=1.0458 (Rec=1.0132, Trip=0.0326)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.834936
      🔧 三元组损失 (Triplet): 0.032522
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.834936 = 0.834936
      📝 加权三元组损失: 1.000 × 0.032522 = 0.032522
      🎯 总损失 (Total): 0.867457
      ✅ 验证公式: 0.867457 = 0.867457
📊 DECL Epoch 7 completed: 30 batches processed

📊 === EPOCH 7 COMPLETED ===
   ⏱️ Epoch time: 2.432s
   ⏱️ Total time so far: 20.135s
   📈 Batches processed: 30
   📈 Average total loss: 0.918880
   📈 Total iterations so far: 240
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #7: loss=0.9188801328341166
   📈 Incremented self.n_epochs to: 8

📅 Starting Epoch 8
🔍 Check termination: n_epochs=15, self.n_epochs=8
🔄 === DECL TRAINING LOOP (Epoch 8) ===
   📊 Batch 5: Total=0.8335 (Rec=0.8015, Trip=0.0320)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.709902
      🔧 三元组损失 (Triplet): 0.032602
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.709902 = 0.709902
      📝 加权三元组损失: 1.000 × 0.032602 = 0.032602
      🎯 总损失 (Total): 0.742505
      ✅ 验证公式: 0.742505 = 0.742505
   📊 Batch 15: Total=0.9561 (Rec=0.9238, Trip=0.0323)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.993765
      🔧 三元组损失 (Triplet): 0.031723
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.993765 = 0.993765
      📝 加权三元组损失: 1.000 × 0.031723 = 0.031723
      🎯 总损失 (Total): 1.025489
      ✅ 验证公式: 1.025489 = 1.025489
   📊 Batch 25: Total=0.7706 (Rec=0.7385, Trip=0.0321)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.756457
      🔧 三元组损失 (Triplet): 0.032108
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.756457 = 0.756457
      📝 加权三元组损失: 1.000 × 0.032108 = 0.032108
      🎯 总损失 (Total): 0.788564
      ✅ 验证公式: 0.788564 = 0.788564
📊 DECL Epoch 8 completed: 30 batches processed

📊 === EPOCH 8 COMPLETED ===
   ⏱️ Epoch time: 2.480s
   ⏱️ Total time so far: 22.615s
   📈 Batches processed: 30
   📈 Average total loss: 0.903547
   📈 Total iterations so far: 270
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #8: loss=0.9035474419593811
   📈 Incremented self.n_epochs to: 9

📅 Starting Epoch 9
🔍 Check termination: n_epochs=15, self.n_epochs=9
🔄 === DECL TRAINING LOOP (Epoch 9) ===
   📊 Batch 5: Total=0.9201 (Rec=0.8879, Trip=0.0322)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.907006
      🔧 三元组损失 (Triplet): 0.032166
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.907006 = 0.907006
      📝 加权三元组损失: 1.000 × 0.032166 = 0.032166
      🎯 总损失 (Total): 0.939172
      ✅ 验证公式: 0.939172 = 0.939172
   📊 Batch 15: Total=0.7448 (Rec=0.7129, Trip=0.0318)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.839860
      🔧 三元组损失 (Triplet): 0.032517
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.839860 = 0.839860
      📝 加权三元组损失: 1.000 × 0.032517 = 0.032517
      🎯 总损失 (Total): 0.872377
      ✅ 验证公式: 0.872377 = 0.872377
   📊 Batch 25: Total=1.0316 (Rec=0.9990, Trip=0.0325)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.910460
      🔧 三元组损失 (Triplet): 0.032319
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.910460 = 0.910460
      📝 加权三元组损失: 1.000 × 0.032319 = 0.032319
      🎯 总损失 (Total): 0.942780
      ✅ 验证公式: 0.942780 = 0.942780
📊 DECL Epoch 9 completed: 30 batches processed

📊 === EPOCH 9 COMPLETED ===
   ⏱️ Epoch time: 2.273s
   ⏱️ Total time so far: 24.888s
   📈 Batches processed: 30
   📈 Average total loss: 0.955288
   📈 Total iterations so far: 300
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #9: loss=0.9552877604961395
   📈 Incremented self.n_epochs to: 10

📅 Starting Epoch 10
🔍 Check termination: n_epochs=15, self.n_epochs=10
🔄 === DECL TRAINING LOOP (Epoch 10) ===
   📊 Batch 5: Total=0.9793 (Rec=0.9472, Trip=0.0321)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.762848
      🔧 三元组损失 (Triplet): 0.031806
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.762848 = 0.762848
      📝 加权三元组损失: 1.000 × 0.031806 = 0.031806
      🎯 总损失 (Total): 0.794653
      ✅ 验证公式: 0.794653 = 0.794653
   📊 Batch 15: Total=0.8251 (Rec=0.7930, Trip=0.0321)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.849115
      🔧 三元组损失 (Triplet): 0.031404
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.849115 = 0.849115
      📝 加权三元组损失: 1.000 × 0.031404 = 0.031404
      🎯 总损失 (Total): 0.880519
      ✅ 验证公式: 0.880519 = 0.880519
   📊 Batch 25: Total=0.7702 (Rec=0.7382, Trip=0.0320)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.720737
      🔧 三元组损失 (Triplet): 0.032057
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.720737 = 0.720737
      📝 加权三元组损失: 1.000 × 0.032057 = 0.032057
      🎯 总损失 (Total): 0.752793
      ✅ 验证公式: 0.752793 = 0.752793
📊 DECL Epoch 10 completed: 30 batches processed

📊 === EPOCH 10 COMPLETED ===
   ⏱️ Epoch time: 2.425s
   ⏱️ Total time so far: 27.313s
   📈 Batches processed: 30
   📈 Average total loss: 0.910963
   📈 Total iterations so far: 330
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #10: loss=0.9109628240267436
   📈 Incremented self.n_epochs to: 11

📅 Starting Epoch 11
🔍 Check termination: n_epochs=15, self.n_epochs=11
🔄 === DECL TRAINING LOOP (Epoch 11) ===
   📊 Batch 5: Total=0.7628 (Rec=0.7313, Trip=0.0315)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.684365
      🔧 三元组损失 (Triplet): 0.031483
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.684365 = 0.684365
      📝 加权三元组损失: 1.000 × 0.031483 = 0.031483
      🎯 总损失 (Total): 0.715848
      ✅ 验证公式: 0.715848 = 0.715848
   📊 Batch 15: Total=0.7515 (Rec=0.7203, Trip=0.0312)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.857441
      🔧 三元组损失 (Triplet): 0.031249
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.857441 = 0.857441
      📝 加权三元组损失: 1.000 × 0.031249 = 0.031249
      🎯 总损失 (Total): 0.888691
      ✅ 验证公式: 0.888691 = 0.888691
   📊 Batch 25: Total=0.7760 (Rec=0.7445, Trip=0.0316)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.809561
      🔧 三元组损失 (Triplet): 0.031967
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.809561 = 0.809561
      📝 加权三元组损失: 1.000 × 0.031967 = 0.031967
      🎯 总损失 (Total): 0.841528
      ✅ 验证公式: 0.841528 = 0.841528
📊 DECL Epoch 11 completed: 30 batches processed

📊 === EPOCH 11 COMPLETED ===
   ⏱️ Epoch time: 2.393s
   ⏱️ Total time so far: 29.707s
   📈 Batches processed: 30
   📈 Average total loss: 0.915647
   📈 Total iterations so far: 360
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #11: loss=0.9156471868356069
   📈 Incremented self.n_epochs to: 12

📅 Starting Epoch 12
🔍 Check termination: n_epochs=15, self.n_epochs=12
🔄 === DECL TRAINING LOOP (Epoch 12) ===
   📊 Batch 5: Total=0.8457 (Rec=0.8143, Trip=0.0314)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.756197
      🔧 三元组损失 (Triplet): 0.031301
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.756197 = 0.756197
      📝 加权三元组损失: 1.000 × 0.031301 = 0.031301
      🎯 总损失 (Total): 0.787498
      ✅ 验证公式: 0.787498 = 0.787498
   📊 Batch 15: Total=0.8440 (Rec=0.8131, Trip=0.0310)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.863748
      🔧 三元组损失 (Triplet): 0.031854
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.863748 = 0.863748
      📝 加权三元组损失: 1.000 × 0.031854 = 0.031854
      🎯 总损失 (Total): 0.895602
      ✅ 验证公式: 0.895602 = 0.895602
   📊 Batch 25: Total=0.9899 (Rec=0.9587, Trip=0.0312)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 1.287587
      🔧 三元组损失 (Triplet): 0.030916
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 1.287587 = 1.287587
      📝 加权三元组损失: 1.000 × 0.030916 = 0.030916
      🎯 总损失 (Total): 1.318503
      ✅ 验证公式: 1.318503 = 1.318503
📊 DECL Epoch 12 completed: 30 batches processed

📊 === EPOCH 12 COMPLETED ===
   ⏱️ Epoch time: 2.575s
   ⏱️ Total time so far: 32.281s
   📈 Batches processed: 30
   📈 Average total loss: 0.940044
   📈 Total iterations so far: 390
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #12: loss=0.9400441527366639
   📈 Incremented self.n_epochs to: 13

📅 Starting Epoch 13
🔍 Check termination: n_epochs=15, self.n_epochs=13
🔄 === DECL TRAINING LOOP (Epoch 13) ===
   📊 Batch 5: Total=0.9453 (Rec=0.9142, Trip=0.0311)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.713865
      🔧 三元组损失 (Triplet): 0.031141
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.713865 = 0.713865
      📝 加权三元组损失: 1.000 × 0.031141 = 0.031141
      🎯 总损失 (Total): 0.745006
      ✅ 验证公式: 0.745006 = 0.745006
   📊 Batch 15: Total=1.1498 (Rec=1.1187, Trip=0.0311)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.963868
      🔧 三元组损失 (Triplet): 0.031777
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.963868 = 0.963868
      📝 加权三元组损失: 1.000 × 0.031777 = 0.031777
      🎯 总损失 (Total): 0.995646
      ✅ 验证公式: 0.995646 = 0.995646
   📊 Batch 25: Total=0.7920 (Rec=0.7611, Trip=0.0309)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.733869
      🔧 三元组损失 (Triplet): 0.031359
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.733869 = 0.733869
      📝 加权三元组损失: 1.000 × 0.031359 = 0.031359
      🎯 总损失 (Total): 0.765228
      ✅ 验证公式: 0.765228 = 0.765228
📊 DECL Epoch 13 completed: 30 batches processed

📊 === EPOCH 13 COMPLETED ===
   ⏱️ Epoch time: 2.491s
   ⏱️ Total time so far: 34.773s
   📈 Batches processed: 30
   📈 Average total loss: 0.905198
   📈 Total iterations so far: 420
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #13: loss=0.905197940270106
   📈 Incremented self.n_epochs to: 14

📅 Starting Epoch 14
🔍 Check termination: n_epochs=15, self.n_epochs=14
🔄 === DECL TRAINING LOOP (Epoch 14) ===
   📊 Batch 5: Total=0.9512 (Rec=0.9202, Trip=0.0311)
   📊 === 详细损失分解 (Batch 10/30) ===
      🔧 重构损失 (Reconstruction): 0.807517
      🔧 三元组损失 (Triplet): 0.031006
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.807517 = 0.807517
      📝 加权三元组损失: 1.000 × 0.031006 = 0.031006
      🎯 总损失 (Total): 0.838522
      ✅ 验证公式: 0.838522 = 0.838522
   📊 Batch 15: Total=0.8266 (Rec=0.7951, Trip=0.0315)
🔄 Batch 20/30
   📊 === 详细损失分解 (Batch 20/30) ===
      🔧 重构损失 (Reconstruction): 0.887252
      🔧 三元组损失 (Triplet): 0.030955
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.887252 = 0.887252
      📝 加权三元组损失: 1.000 × 0.030955 = 0.030955
      🎯 总损失 (Total): 0.918207
      ✅ 验证公式: 0.918207 = 0.918207
   📊 Batch 25: Total=0.8009 (Rec=0.7701, Trip=0.0309)
🔄 Batch 30/30
   📊 === 详细损失分解 (Batch 30/30) ===
      🔧 重构损失 (Reconstruction): 0.869649
      🔧 三元组损失 (Triplet): 0.031225
      ✅ 分离损失 (Separation): 已移除，不参与计算
      📝 加权重构损失: 1.000 × 0.869649 = 0.869649
      📝 加权三元组损失: 1.000 × 0.031225 = 0.031225
      🎯 总损失 (Total): 0.900874
      ✅ 验证公式: 0.900874 = 0.900874
📊 DECL Epoch 14 completed: 30 batches processed

📊 === EPOCH 14 COMPLETED ===
   ⏱️ Epoch time: 2.673s
   ⏱️ Total time so far: 37.445s
   📈 Batches processed: 30
   📈 Average total loss: 0.888978
   📈 Total iterations so far: 450
   🔍 Interrupted flag: False
   📊 === 损失配置确认 ===
      ✅ 当前模式: DECL模式
      ✅ 当前使用的损失组件: 重构损失 + 三元组损失
      🔧 损失权重: w_rec=1.0, w_triplet=1.0
      📝 损失公式: Total = 1.0 × Reconstruction + 1.0 × Triplet
      ✅ 分离损失状态: 已完全移除
      ✅ w_sep属性: 已移除
Epoch #14: loss=0.8889780720074971
   📈 Incremented self.n_epochs to: 15

📅 Starting Epoch 15
🔍 Check termination: n_epochs=15, self.n_epochs=15
🛑 Breaking due to epoch limit: 15 >= 15

🏁 === TRAINING COMPLETED ===
   ⏱️ Total training time: 37.445s
   📈 Total epochs completed: 15
   📈 Total iterations completed: 450
   📈 Final loss: 0.8889780720074971
   📊 Loss history: [1.1559963166713714, 1.1165544867515564, 0.9623527328173319, 0.9979515333970388, 0.9433158258597056, 0.9396913846333822, 0.9816121220588684, 0.9188801328341166, 0.9035474419593811, 0.9552877604961395, 0.9109628240267436, 0.9156471868356069, 0.9400441527366639, 0.905197940270106, 0.8889780720074971]

Training time: 0:00:37.861228

Evaluation result: {'f1': 0.5161895823557016, 'precision': 0.3843466107617051, 'recall': 0.7857142857142857, 'infer_time': 23.983654260635376}
Finished.
