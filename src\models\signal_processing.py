"""
🚀 信号处理优化模块
专门用于改进信号重构和去噪的效果，解决当前两种重构目标性能相似的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class AdaptiveSignalFilter(nn.Module):
    """
    🎯 自适应信号滤波器
    使用可学习的滤波器参数来改进信号质量
    """
    def __init__(self, signal_dim, filter_size=5, num_filters=3):
        super(AdaptiveSignalFilter, self).__init__()
        self.signal_dim = signal_dim
        self.filter_size = filter_size
        self.num_filters = num_filters
        
        # 可学习的滤波器权重
        self.filters = nn.Parameter(torch.randn(num_filters, 1, filter_size))
        self.filter_weights = nn.Parameter(torch.ones(num_filters) / num_filters)
        
        # 归一化层
        self.norm = nn.LayerNorm(signal_dim)
        
    def forward(self, x):
        """
        Args:
            x: 输入信号 (B, T, D)
        Returns:
            filtered_x: 滤波后的信号 (B, T, D)
        """
        try:
            B, T, D = x.shape

            # 对每个特征维度分别应用滤波
            filtered_signals = []

            for i in range(D):
                signal_1d = x[:, :, i].unsqueeze(1)  # (B, 1, T)

                # 应用多个滤波器
                filter_outputs = []
                for j in range(self.num_filters):
                    try:
                        # 使用1D卷积进行滤波
                        filtered = F.conv1d(signal_1d, self.filters[j:j+1], padding=self.filter_size//2)
                        filter_outputs.append(filtered)
                    except Exception as e:
                        print(f"⚠️ 滤波器{j}处理失败: {e}, 使用原始信号")
                        filter_outputs.append(signal_1d)

                if filter_outputs:
                    # 加权组合滤波器输出
                    combined = torch.stack(filter_outputs, dim=0)  # (num_filters, B, 1, T)
                    weights = F.softmax(self.filter_weights, dim=0).view(-1, 1, 1, 1)
                    weighted_output = (combined * weights).sum(dim=0)  # (B, 1, T)
                    filtered_signals.append(weighted_output.squeeze(1))  # (B, T)
                else:
                    # 如果所有滤波器都失败，使用原始信号
                    filtered_signals.append(x[:, :, i])

            # 重新组合所有特征维度
            filtered_x = torch.stack(filtered_signals, dim=2)  # (B, T, D)

            # 应用归一化
            filtered_x = self.norm(filtered_x)

            return filtered_x

        except Exception as e:
            print(f"⚠️ 自适应滤波器处理失败: {e}, 返回原始输入")
            return x


class MultiScaleTemporalAnalyzer(nn.Module):
    """
    🔍 多尺度时间分析器
    在不同时间尺度上分析信号模式，提高异常检测的准确性
    """
    def __init__(self, input_dim, scales=[1, 2, 4, 8]):
        super(MultiScaleTemporalAnalyzer, self).__init__()
        self.scales = scales
        self.input_dim = input_dim

        # 为每个尺度创建分析器 - 修复padding计算
        self.scale_analyzers = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(input_dim, input_dim, kernel_size=3, padding=scale, dilation=scale),
                nn.ReLU(),
                nn.Conv1d(input_dim, input_dim, kernel_size=3, padding=scale, dilation=scale),
                nn.ReLU()
            ) for scale in scales
        ])

        # 融合不同尺度的特征
        self.fusion = nn.Sequential(
            nn.Linear(input_dim * len(scales), input_dim * 2),
            nn.ReLU(),
            nn.Linear(input_dim * 2, input_dim),
            nn.Tanh()
        )
        
    def forward(self, x):
        """
        Args:
            x: 输入信号 (B, T, D)
        Returns:
            multi_scale_features: 多尺度特征 (B, T, D)
        """
        B, T, D = x.shape
        x_transposed = x.transpose(1, 2)  # (B, D, T)

        # 在不同尺度上分析
        scale_features = []
        target_length = None

        for i, analyzer in enumerate(self.scale_analyzers):
            try:
                features = analyzer(x_transposed)  # (B, D, T')
                features_transposed = features.transpose(1, 2)  # (B, T', D)

                # 确定目标长度（使用第一个尺度的长度作为基准）
                if target_length is None:
                    target_length = features_transposed.size(1)

                # 如果长度不匹配，进行插值对齐
                current_length = features_transposed.size(1)
                if current_length != target_length:
                    # 使用线性插值调整时间维度
                    features_transposed = F.interpolate(
                        features_transposed.transpose(1, 2),  # (B, D, T')
                        size=target_length,
                        mode='linear',
                        align_corners=False
                    ).transpose(1, 2)  # (B, T, D)

                scale_features.append(features_transposed)

            except Exception as e:
                print(f"⚠️ 多尺度分析器第{i}个尺度处理失败: {e}")
                # 创建fallback特征（零填充）
                fallback_features = torch.zeros(B, target_length or T, D, device=x.device)
                scale_features.append(fallback_features)

        # 确保所有特征具有相同的时间维度
        if scale_features:
            # 再次检查维度一致性
            lengths = [f.size(1) for f in scale_features]
            if len(set(lengths)) > 1:
                print(f"⚠️ 检测到不一致的时间维度: {lengths}, 强制对齐到 {target_length}")
                aligned_features = []
                for features in scale_features:
                    if features.size(1) != target_length:
                        features = F.interpolate(
                            features.transpose(1, 2),
                            size=target_length,
                            mode='linear',
                            align_corners=False
                        ).transpose(1, 2)
                    aligned_features.append(features)
                scale_features = aligned_features

            # 拼接所有尺度的特征
            try:
                concatenated = torch.cat(scale_features, dim=2)  # (B, T, D*num_scales)
                # 融合特征
                fused_features = self.fusion(concatenated)  # (B, T, D)
                return fused_features
            except Exception as e:
                print(f"⚠️ 特征拼接失败: {e}, 返回原始输入")
                return x
        else:
            print("⚠️ 没有有效的多尺度特征，返回原始输入")
            return x


class EnhancedDenoiser(nn.Module):
    """
    🧹 增强去噪器
    结合自适应滤波和多尺度分析的高级去噪模块
    """
    def __init__(self, signal_dim, noise_level_predictor=True):
        super(EnhancedDenoiser, self).__init__()
        self.signal_dim = signal_dim
        
        # 自适应滤波器
        self.adaptive_filter = AdaptiveSignalFilter(signal_dim)
        
        # 多尺度分析器
        self.multi_scale_analyzer = MultiScaleTemporalAnalyzer(signal_dim)
        
        # 噪声水平预测器
        if noise_level_predictor:
            self.noise_predictor = nn.Sequential(
                nn.Linear(signal_dim, signal_dim // 2),
                nn.ReLU(),
                nn.Linear(signal_dim // 2, 1),
                nn.Sigmoid()
            )
        else:
            self.noise_predictor = None
            
        # 最终去噪层
        self.denoising_head = nn.Sequential(
            nn.Linear(signal_dim * 2, signal_dim),
            nn.ReLU(),
            nn.Linear(signal_dim, signal_dim)
        )
        
    def forward(self, noisy_signal):
        """
        Args:
            noisy_signal: 含噪信号 (B, T, D)
        Returns:
            denoised_signal: 去噪信号 (B, T, D)
            noise_level: 预测的噪声水平 (B, T, 1) 可选
        """
        try:
            # 自适应滤波
            filtered_signal = self.adaptive_filter(noisy_signal)

            # 多尺度分析
            multi_scale_features = self.multi_scale_analyzer(filtered_signal)

            # 确保维度匹配
            if filtered_signal.shape != multi_scale_features.shape:
                print(f"⚠️ 维度不匹配: filtered_signal {filtered_signal.shape} vs multi_scale_features {multi_scale_features.shape}")
                # 对齐时间维度
                target_length = filtered_signal.size(1)
                if multi_scale_features.size(1) != target_length:
                    multi_scale_features = F.interpolate(
                        multi_scale_features.transpose(1, 2),
                        size=target_length,
                        mode='linear',
                        align_corners=False
                    ).transpose(1, 2)

            # 拼接原始滤波信号和多尺度特征
            combined_features = torch.cat([filtered_signal, multi_scale_features], dim=2)

            # 最终去噪
            denoised_signal = self.denoising_head(combined_features)

            # 残差连接
            denoised_signal = denoised_signal + filtered_signal

            # 预测噪声水平（可选）
            noise_level = None
            if self.noise_predictor is not None:
                noise_level = self.noise_predictor(denoised_signal)

            return denoised_signal, noise_level

        except Exception as e:
            print(f"⚠️ 增强去噪器处理失败: {e}, 返回原始输入")
            # 返回原始输入作为fallback
            noise_level = None
            if self.noise_predictor is not None:
                try:
                    noise_level = self.noise_predictor(noisy_signal)
                except:
                    noise_level = torch.ones(noisy_signal.size(0), noisy_signal.size(1), 1, device=noisy_signal.device) * 0.5
            return noisy_signal, noise_level


def apply_window_function(signal, window_type='hann'):
    """
    🪟 应用窗函数进行信号处理
    
    Args:
        signal: 输入信号 (B, T, D) 或 (T,)
        window_type: 窗函数类型
    
    Returns:
        windowed_signal: 加窗后的信号
    """
    if isinstance(signal, torch.Tensor):
        device = signal.device
        if signal.dim() == 1:
            T = signal.size(0)
            if window_type == 'hann':
                window = torch.hann_window(T, device=device)
            elif window_type == 'hamming':
                window = torch.hamming_window(T, device=device)
            elif window_type == 'blackman':
                window = torch.blackman_window(T, device=device)
            else:
                window = torch.ones(T, device=device)
            
            return signal * window
        
        elif signal.dim() == 3:
            B, T, D = signal.shape
            if window_type == 'hann':
                window = torch.hann_window(T, device=device)
            elif window_type == 'hamming':
                window = torch.hamming_window(T, device=device)
            elif window_type == 'blackman':
                window = torch.blackman_window(T, device=device)
            else:
                window = torch.ones(T, device=device)
            
            window = window.view(1, T, 1).expand(B, T, D)
            return signal * window
    
    return signal


def spectral_denoising(signal, noise_threshold=0.1):
    """
    🌊 基于频谱的去噪方法
    
    Args:
        signal: 输入信号 (B, T, D)
        noise_threshold: 噪声阈值
    
    Returns:
        denoised_signal: 去噪后的信号
    """
    if not isinstance(signal, torch.Tensor):
        signal = torch.tensor(signal, dtype=torch.float32)
    
    B, T, D = signal.shape
    denoised_signals = []
    
    for d in range(D):
        signal_1d = signal[:, :, d]  # (B, T)
        
        # 对每个批次进行FFT
        fft_signal = torch.fft.fft(signal_1d, dim=1)
        
        # 计算功率谱
        power_spectrum = torch.abs(fft_signal) ** 2
        
        # 基于功率谱的阈值去噪
        threshold = noise_threshold * torch.max(power_spectrum, dim=1, keepdim=True)[0]
        mask = power_spectrum > threshold
        
        # 应用掩码
        filtered_fft = fft_signal * mask.float()
        
        # 逆FFT
        denoised_1d = torch.fft.ifft(filtered_fft, dim=1).real
        denoised_signals.append(denoised_1d)
    
    denoised_signal = torch.stack(denoised_signals, dim=2)
    return denoised_signal


class SignalQualityAssessment(nn.Module):
    """
    📊 信号质量评估器
    评估信号的质量，用于指导去噪过程
    """
    def __init__(self, signal_dim):
        super(SignalQualityAssessment, self).__init__()
        self.signal_dim = signal_dim
        self.quality_net = None  # 延迟初始化

    def _build_quality_net(self, actual_dim):
        """根据实际输入维度构建质量评估网络"""
        if self.quality_net is None or actual_dim != self.signal_dim:
            print(f"🔧 构建质量评估网络: 输入维度 {actual_dim}")
            self.signal_dim = actual_dim
            self.quality_net = nn.Sequential(
                nn.Linear(actual_dim, max(actual_dim // 2, 4)),
                nn.ReLU(),
                nn.Linear(max(actual_dim // 2, 4), max(actual_dim // 4, 2)),
                nn.ReLU(),
                nn.Linear(max(actual_dim // 4, 2), 1),
                nn.Sigmoid()
            )
            # 移动到正确的设备
            if hasattr(self, '_device'):
                self.quality_net = self.quality_net.to(self._device)

    def forward(self, signal):
        """
        Args:
            signal: 输入信号 (B, T, D)
        Returns:
            quality_scores: 质量分数 (B, T, 1)
        """
        try:
            B, T, D = signal.shape

            # 记录设备信息
            self._device = signal.device

            # 根据实际维度构建网络
            self._build_quality_net(D)

            # 前向传播
            quality_scores = self.quality_net(signal)
            return quality_scores

        except Exception as e:
            print(f"⚠️ 信号质量评估失败: {e}")
            # 返回默认质量分数（中等质量）
            B, T, _ = signal.shape
            return torch.ones(B, T, 1, device=signal.device) * 0.5
