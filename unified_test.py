#!/usr/bin/env python3
"""
🔄 TimesURL统一测试程序
支持多种测试模式：基线测试、参数对比测试、详细损失分析等

使用方法:
  python unified_test.py --mode baseline --epochs 15          # 基线测试
  python unified_test.py --mode compare                       # 参数对比测试  
  python unified_test.py --mode analysis --detailed           # 详细分析
"""

import subprocess
import json
import re
import sys
import os
import argparse
from datetime import datetime

def run_baseline_test(epochs=15, detailed_analysis=False):
    """运行基线测试"""

    test_name = f"baseline_test_{epochs}epochs"
    if detailed_analysis:
        test_name += "_detailed"
    
    cmd = [
        "python", "src/train.py", 
        "ConstPos", test_name,
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", str(epochs),
        "--batch-size", "16",
        "--lr", "0.001",
        "--gpu", "0",
        "--reconstruct_target", "original",
        # 基线参数 - 损失权重都设置为1
        "--lambda_decl", "1.0",
        "--temp_contrast", "0.5",
        "--lambda_align", "0.1"
    ]
    
    try:
        print(f"🔄 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)  # 20分钟超时
        
        if result.returncode != 0:
            print(f"❌ 测试失败:")
            print(f"   stderr: {result.stderr}")
            return None
        
        output = result.stdout
        print(f"✅ 训练完成，解析结果...")
        
        # 保存完整输出用于调试
        output_file = f"{test_name}_output.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(output)
        print(f"📁 完整输出已保存到: {output_file}")
        
        # 解析性能指标
        f1_score, precision, recall = parse_performance_metrics(output)
        
        result_data = {
            "test_name": test_name,
            "epochs": epochs,
            "f1_score": f1_score,
            "precision": precision,
            "recall": recall,
            "output_file": output_file
        }
        
        print(f"📊 测试结果:")
        print(f"   F1: {f1_score:.4f}")
        print(f"   Precision: {precision:.4f}")
        print(f"   Recall: {recall:.4f}")
        
        if detailed_analysis:
            analyze_loss_output(output)
        
        return result_data
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时")
        return None
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def run_parameter_comparison_test():
    """运行参数对比测试"""
    
    print("\n🔬 === 参数对比测试 ===")
    print("🎯 目标: 对比不同参数设置的影响")
    
    test_configs = [
        {
            "name": "equal_weights_15epochs",
            "epochs": 15,
            "lambda_decl": 1.0,
            "temp_contrast": 0.5,
            "lambda_align": 0.1,
            "description": "等权重配置 (15轮) - w_rec=1.0, w_triplet=1.0"
        },
        {
            "name": "equal_weights_5epochs",
            "epochs": 5,
            "lambda_decl": 1.0,
            "temp_contrast": 0.5,
            "lambda_align": 0.1,
            "description": "等权重配置 (5轮) - w_rec=1.0, w_triplet=1.0"
        },
        {
            "name": "original_weights",
            "epochs": 15,
            "lambda_decl": 0.1,
            "temp_contrast": 0.5,
            "lambda_align": 0.1,
            "description": "原始权重配置 - w_rec=1.0, w_triplet=0.1"
        }
    ]
    
    results = []
    for config in test_configs:
        print(f"\n🧪 测试配置: {config['description']}")
        
        cmd = [
            "python", "src/train.py", 
            "ConstPos", config["name"],
            "--loader", "user_anomaly", 
            "--eval",
            "--epochs", str(config["epochs"]),
            "--batch-size", "16",
            "--lr", "0.001",
            "--gpu", "0",
            "--reconstruct_target", "original",
            "--lambda_decl", str(config["lambda_decl"]),
            "--temp_contrast", str(config["temp_contrast"]),
            "--lambda_align", str(config["lambda_align"])
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)
            if result.returncode == 0:
                f1, precision, recall = parse_performance_metrics(result.stdout)
                results.append({
                    "config": config,
                    "f1": f1,
                    "precision": precision,
                    "recall": recall
                })
                print(f"   📊 结果: F1={f1:.4f}, P={precision:.4f}, R={recall:.4f}")
            else:
                print(f"   ❌ 测试失败")
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    # 分析结果
    if results:
        print(f"\n📈 === 参数对比分析 ===")
        best_f1 = max(results, key=lambda x: x["f1"])
        print(f"🏆 最佳F1配置: {best_f1['config']['description']}")
        print(f"   F1: {best_f1['f1']:.4f}, P: {best_f1['precision']:.4f}, R: {best_f1['recall']:.4f}")
        
        for result in results:
            config = result["config"]
            print(f"📋 {config['description']}: F1={result['f1']:.4f} (Δ{result['f1']-best_f1['f1']:+.4f})")
    
    return results

def analyze_loss_output(output):
    """分析损失输出"""
    
    print(f"\n🔍 === 损失分析 ===")
    
    lines = output.split('\n')
    
    # 检查模式
    decl_mode = any("DECL模式" in line for line in lines)
    print(f"🎯 训练模式: {'DECL模式' if decl_mode else '非DECL模式'}")
    
    # 检查分离损失移除确认
    separation_removed = any("分离损失状态: 已完全移除" in line for line in lines)
    w_sep_removed = any("w_sep属性: 已移除" in line for line in lines)
    
    print(f"✅ 分离损失移除确认: {'是' if separation_removed else '否'}")
    print(f"✅ w_sep属性移除确认: {'是' if w_sep_removed else '否'}")
    
    # 统计损失打印次数
    detailed_loss_count = sum(1 for line in lines if "详细损失分解" in line)
    print(f"📊 详细损失分解打印次数: {detailed_loss_count}")
    
    # 提取最终损失值
    final_losses = []
    for line in lines:
        if "Final loss:" in line:
            match = re.search(r"Final loss:\s*([0-9.]+)", line)
            if match:
                final_losses.append(float(match.group(1)))
    
    if final_losses:
        print(f"📈 最终损失: {final_losses[-1]:.6f}")

def parse_performance_metrics(output):
    """解析性能指标"""
    
    # 首先尝试匹配 "Evaluation result:" 格式
    eval_result_match = re.search(r"Evaluation result:\s*\{([^}]+)\}", output)
    if eval_result_match:
        result_str = eval_result_match.group(1)
        # 解析字典格式的结果
        f1_match = re.search(r"'f1':\s*([0-9.]+)", result_str)
        precision_match = re.search(r"'precision':\s*([0-9.]+)", result_str)
        recall_match = re.search(r"'recall':\s*([0-9.]+)", result_str)
        
        f1_score = float(f1_match.group(1)) if f1_match else 0.0
        precision = float(precision_match.group(1)) if precision_match else 0.0
        recall = float(recall_match.group(1)) if recall_match else 0.0
        
        return f1_score, precision, recall
    
    # 如果没有找到，返回0
    print("⚠️ 未找到性能指标，可能解析失败")
    return 0.0, 0.0, 0.0

def main():
    """主测试流程"""
    parser = argparse.ArgumentParser(description="TimesURL统一测试程序")
    parser.add_argument("--mode", choices=["baseline", "compare", "analysis"], default="baseline",
                       help="测试模式: baseline(基线测试), compare(参数对比), analysis(详细分析)")
    parser.add_argument("--epochs", type=int, default=15, help="训练轮数")
    parser.add_argument("--detailed", action="store_true", help="启用详细损失分析")
    
    args = parser.parse_args()
    
    print("🔄 === TimesURL统一测试程序 ===")
    print(f"🎯 测试模式: {args.mode}")
    
    if args.mode == "baseline":
        print("🎯 目标: 验证基线配置的性能")
        result = run_baseline_test(epochs=args.epochs, detailed_analysis=args.detailed)
        
        if result:
            print(f"\n✅ 基线测试完成!")
            print(f"📊 结果: F1={result['f1_score']:.4f}, P={result['precision']:.4f}, R={result['recall']:.4f}")
            
            # 与期望结果对比
            expected_f1 = 0.560
            f1_diff = abs(result['f1_score'] - expected_f1)
            
            if f1_diff < 0.05:
                print(f"🎉 结果接近期望值 (F1差异: {f1_diff:.4f})")
            else:
                print(f"⚠️ 结果与期望值有差异 (F1差异: {f1_diff:.4f})")
                print(f"💡 可能原因:")
                print(f"   - 训练轮数不足 (当前{args.epochs}轮)")
                print(f"   - 随机种子差异")
                print(f"   - 数据或环境差异")
        else:
            print(f"\n❌ 基线测试失败!")
    
    elif args.mode == "compare":
        print("🎯 目标: 对比不同参数配置的性能")
        results = run_parameter_comparison_test()
        
        if results:
            print(f"\n✅ 参数对比测试完成!")
            print(f"📊 共测试了 {len(results)} 种配置")
        else:
            print(f"\n❌ 参数对比测试失败!")
    
    elif args.mode == "analysis":
        print("🎯 目标: 详细分析训练过程和损失")
        result = run_baseline_test(epochs=args.epochs, detailed_analysis=True)
        
        if result:
            print(f"\n✅ 详细分析完成!")
            print(f"📁 详细输出文件: {result['output_file']}")
        else:
            print(f"\n❌ 详细分析失败!")
    
    print(f"\n🏁 测试完成!")

if __name__ == "__main__":
    main()
