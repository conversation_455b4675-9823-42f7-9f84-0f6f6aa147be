#!/usr/bin/env python3
"""
🧪 信号处理模块修复测试
测试修复后的信号处理组件是否能正常工作，不再出现维度不匹配错误
"""

import torch
import numpy as np
import sys
import os

# 添加src路径
sys.path.append('src')

def test_adaptive_signal_filter():
    """测试自适应信号滤波器"""
    print("🧪 测试自适应信号滤波器...")
    
    try:
        from models.signal_processing import AdaptiveSignalFilter
        
        # 创建测试数据
        B, T, D = 4, 50, 8
        test_input = torch.randn(B, T, D)
        
        # 创建滤波器
        filter_module = AdaptiveSignalFilter(signal_dim=D)
        
        # 测试前向传播
        output = filter_module(test_input)
        
        print(f"   ✅ 输入形状: {test_input.shape}")
        print(f"   ✅ 输出形状: {output.shape}")
        
        # 检查形状是否匹配
        if output.shape == test_input.shape:
            print("   ✅ 自适应信号滤波器测试通过!")
            return True
        else:
            print(f"   ❌ 形状不匹配: 期望 {test_input.shape}, 得到 {output.shape}")
            return False
            
    except Exception as e:
        print(f"   ❌ 自适应信号滤波器测试失败: {e}")
        return False

def test_multi_scale_temporal_analyzer():
    """测试多尺度时间分析器"""
    print("🧪 测试多尺度时间分析器...")
    
    try:
        from models.signal_processing import MultiScaleTemporalAnalyzer
        
        # 创建测试数据
        B, T, D = 4, 50, 8
        test_input = torch.randn(B, T, D)
        
        # 创建分析器
        analyzer = MultiScaleTemporalAnalyzer(input_dim=D, scales=[1, 2, 4, 8])
        
        # 测试前向传播
        output = analyzer(test_input)
        
        print(f"   ✅ 输入形状: {test_input.shape}")
        print(f"   ✅ 输出形状: {output.shape}")
        
        # 检查形状是否匹配
        if output.shape == test_input.shape:
            print("   ✅ 多尺度时间分析器测试通过!")
            return True
        else:
            print(f"   ❌ 形状不匹配: 期望 {test_input.shape}, 得到 {output.shape}")
            return False
            
    except Exception as e:
        print(f"   ❌ 多尺度时间分析器测试失败: {e}")
        return False

def test_enhanced_denoiser():
    """测试增强去噪器"""
    print("🧪 测试增强去噪器...")
    
    try:
        from models.signal_processing import EnhancedDenoiser
        
        # 创建测试数据
        B, T, D = 4, 50, 8
        test_input = torch.randn(B, T, D)
        
        # 创建去噪器
        denoiser = EnhancedDenoiser(signal_dim=D, noise_level_predictor=True)
        
        # 测试前向传播
        denoised_output, noise_level = denoiser(test_input)
        
        print(f"   ✅ 输入形状: {test_input.shape}")
        print(f"   ✅ 去噪输出形状: {denoised_output.shape}")
        if noise_level is not None:
            print(f"   ✅ 噪声水平形状: {noise_level.shape}")
        
        # 检查形状是否匹配
        if denoised_output.shape == test_input.shape:
            print("   ✅ 增强去噪器测试通过!")
            return True
        else:
            print(f"   ❌ 形状不匹配: 期望 {test_input.shape}, 得到 {denoised_output.shape}")
            return False
            
    except Exception as e:
        print(f"   ❌ 增强去噪器测试失败: {e}")
        return False

def test_different_input_sizes():
    """测试不同输入尺寸的鲁棒性"""
    print("🧪 测试不同输入尺寸的鲁棒性...")
    
    try:
        from models.signal_processing import EnhancedDenoiser
        
        # 测试不同的输入尺寸
        test_cases = [
            (2, 30, 4),   # 小尺寸
            (8, 100, 16), # 大尺寸
            (1, 25, 8),   # 单样本
            (4, 46, 8),   # 问题尺寸（原来导致错误的尺寸）
        ]
        
        success_count = 0
        
        for i, (B, T, D) in enumerate(test_cases):
            try:
                test_input = torch.randn(B, T, D)
                denoiser = EnhancedDenoiser(signal_dim=D)
                
                denoised_output, noise_level = denoiser(test_input)
                
                if denoised_output.shape == test_input.shape:
                    print(f"   ✅ 测试用例 {i+1} ({B}, {T}, {D}): 通过")
                    success_count += 1
                else:
                    print(f"   ❌ 测试用例 {i+1} ({B}, {T}, {D}): 形状不匹配")
                    
            except Exception as e:
                print(f"   ❌ 测试用例 {i+1} ({B}, {T}, {D}): 失败 - {e}")
        
        if success_count == len(test_cases):
            print("   ✅ 所有尺寸测试通过!")
            return True
        else:
            print(f"   ⚠️ {success_count}/{len(test_cases)} 测试通过")
            return success_count > len(test_cases) // 2
            
    except Exception as e:
        print(f"   ❌ 鲁棒性测试失败: {e}")
        return False

def test_integration_with_timesurl():
    """测试与TimesURL的集成"""
    print("🧪 测试与TimesURL的集成...")
    
    try:
        # 模拟TimesURL中的使用场景
        from models.signal_processing import EnhancedDenoiser, SignalQualityAssessment
        
        # 创建测试数据（模拟TimesURL中的特征维度）
        B, T, D = 4, 50, 64  # 假设特征维度为64
        test_input = torch.randn(B, T, D)
        
        # 创建组件
        denoiser = EnhancedDenoiser(signal_dim=D)
        quality_assessor = SignalQualityAssessment(signal_dim=D)
        
        # 测试去噪
        denoised_output, noise_level = denoiser(test_input)
        
        # 测试质量评估
        quality_scores = quality_assessor(denoised_output)
        
        print(f"   ✅ 原始输入形状: {test_input.shape}")
        print(f"   ✅ 去噪输出形状: {denoised_output.shape}")
        print(f"   ✅ 质量分数形状: {quality_scores.shape}")
        
        # 模拟条件选择（类似TimesURL中的逻辑）
        use_enhanced = quality_scores < 0.7
        final_output = torch.where(
            use_enhanced.expand_as(denoised_output),
            denoised_output,
            test_input
        )
        
        print(f"   ✅ 最终输出形状: {final_output.shape}")
        
        if final_output.shape == test_input.shape:
            print("   ✅ TimesURL集成测试通过!")
            return True
        else:
            print(f"   ❌ 集成测试失败: 形状不匹配")
            return False
            
    except Exception as e:
        print(f"   ❌ TimesURL集成测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 === 信号处理模块修复测试 ===")
    print("🎯 目标: 验证修复后的信号处理组件不再出现维度不匹配错误")
    
    tests = [
        ("自适应信号滤波器", test_adaptive_signal_filter),
        ("多尺度时间分析器", test_multi_scale_temporal_analyzer),
        ("增强去噪器", test_enhanced_denoiser),
        ("不同输入尺寸鲁棒性", test_different_input_sizes),
        ("TimesURL集成", test_integration_with_timesurl),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        if result:
            passed_tests += 1
        print(f"{'='*50}")
    
    print(f"\n📊 === 测试结果总结 ===")
    print(f"   通过测试: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("   ✅ 所有测试通过! 信号处理模块修复成功!")
        print("   🚀 可以继续运行优化实验了!")
    elif passed_tests >= total_tests * 0.8:
        print("   ⚠️ 大部分测试通过，但仍有一些问题需要解决")
    else:
        print("   ❌ 多个测试失败，需要进一步修复")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    main()
