import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F


def dcrad_contrastive_loss(z_denoised, z_original, z_noisy, temp=1.0, lambda_align=0.1):
    """
    🚀 修复后的DCRAD对比损失 - 确保正确的对比学习逻辑

    核心原则: 拉近(原始-去噪), 推远(原始-增噪)
    目标: sim(original, denoised) > sim(original, noisy)

    Args:
        z_denoised: 去噪样本表示 (B, T, D) - 正样本
        z_original: 原始样本表示 (B, T, D) - 锚点
        z_noisy: 增噪样本表示 (B, T, D) - 负样本
        temp: 温度系数
        lambda_align: 方向对齐项权重

    Returns:
        total_loss: 总对比损失
    """
    # 归一化表示向量
    eps = 1e-8
    z_denoised = F.normalize(z_denoised, dim=-1, eps=eps)
    z_original = F.normalize(z_original, dim=-1, eps=eps)
    z_noisy = F.normalize(z_noisy, dim=-1, eps=eps)

    # 🎯 修复后的语义对比项 - 确保正确的正负样本关系
    # 计算相似度分数
    pos_sim = F.cosine_similarity(z_original, z_denoised, dim=-1)  # (B, T) - 应该高
    neg_sim = F.cosine_similarity(z_original, z_noisy, dim=-1)     # (B, T) - 应该低

    # 🔧 使用边际损失确保 pos_sim > neg_sim
    # 损失 = max(0, margin - pos_sim + neg_sim)
    # 当 pos_sim > neg_sim + margin 时，损失为0
    margin = 0.2
    contrastive_loss = F.relu(margin - pos_sim + neg_sim)
    semantic_loss = contrastive_loss.mean()

    # 🔍 调试信息 - 验证对比学习方向
    with torch.no_grad():
        avg_pos_sim = pos_sim.mean().item()
        avg_neg_sim = neg_sim.mean().item()
        similarity_gap = avg_pos_sim - avg_neg_sim

        # 如果相似度差异为负，说明逻辑仍然错误
        if similarity_gap < 0:
            print(f"⚠️ 警告: 对比学习逻辑错误! pos_sim={avg_pos_sim:.4f} < neg_sim={avg_neg_sim:.4f}")
        # 每100次迭代打印一次调试信息
        if torch.rand(1).item() < 0.01:  # 1%概率打印
            print(f"🔍 对比学习状态: pos_sim={avg_pos_sim:.4f}, neg_sim={avg_neg_sim:.4f}, gap={similarity_gap:.4f}")
    
    # 🎯 第二项：修复后的去噪方向对齐项
    if lambda_align > 0:
        # 理想去噪方向: 从噪声指向去噪 (noisy -> denoised)
        ideal_direction = z_denoised - z_noisy  # (B, T, D)

        # 实际方向: 从噪声指向原始 (noisy -> original)
        actual_direction = z_original - z_noisy  # (B, T, D)

        # 归一化方向向量
        ideal_direction = F.normalize(ideal_direction, dim=-1, eps=eps)
        actual_direction = F.normalize(actual_direction, dim=-1, eps=eps)

        # 🔧 方向对齐损失: 最大化两个方向的相似度
        # 目标: 原始样本应该在从噪声到去噪的方向上
        direction_similarity = F.cosine_similarity(ideal_direction, actual_direction, dim=-1)
        direction_alignment_loss = torch.mean(1 - direction_similarity)  # 最小化角度差异

        # 🔍 调试方向对齐
        with torch.no_grad():
            avg_direction_sim = direction_similarity.mean().item()
            if torch.rand(1).item() < 0.01:  # 1%概率打印
                print(f"🧭 方向对齐: similarity={avg_direction_sim:.4f}")
    else:
        direction_alignment_loss = torch.tensor(0.0, device=z_original.device)

    # 🎯 总损失 = 对比损失 + 方向对齐损失
    total_loss = semantic_loss + lambda_align * direction_alignment_loss

    # 🔍 最终调试信息
    with torch.no_grad():
        if torch.rand(1).item() < 0.01:  # 1%概率打印
            print(f"📊 DCRAD损失: semantic={semantic_loss.item():.4f}, "
                  f"direction={direction_alignment_loss.item():.4f}, "
                  f"total={total_loss.item():.4f}")

    return total_loss


def decl_triplet_loss(tensor_A_pos, tensor_B_org, tensor_C_neg):
    """保持向后兼容的简单三元组损失"""
    dot_poduct1 = torch.bmm(tensor_A_pos, tensor_B_org.transpose(1, 2))
    dot_poduct2 = torch.bmm(tensor_B_org, tensor_C_neg.transpose(1, 2))
    pos = torch.exp(dot_poduct1)
    neg = torch.exp(dot_poduct2)
    denominator = pos+neg
    loss = torch.mean(-torch.log(torch.div(pos, denominator)))
    return loss


class FocalLoss(nn.Module):
    """
    🔥 Focal Loss用于解决类别不平衡问题
    专门针对异常检测中正常样本过多、异常样本稀少的情况
    """
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        """
        Args:
            inputs: 预测概率 (B, 2) 或 (B,) for binary
            targets: 真实标签 (B,)
        """
        # 确保输入是概率形式
        if inputs.dim() == 1:
            # 二分类情况，转换为概率
            p = torch.sigmoid(inputs)
            ce_loss = F.binary_cross_entropy_with_logits(inputs, targets.float(), reduction='none')
        else:
            # 多分类情况
            p = F.softmax(inputs, dim=1)
            ce_loss = F.cross_entropy(inputs, targets, reduction='none')
            p = p.gather(1, targets.unsqueeze(1)).squeeze(1)

        # 计算focal weight: (1-p)^gamma
        focal_weight = (1 - p) ** self.gamma

        # 应用alpha权重
        if self.alpha is not None:
            if isinstance(self.alpha, (float, int)):
                alpha_weight = self.alpha if targets.sum() > 0 else (1 - self.alpha)
            else:
                alpha_weight = self.alpha[targets]
            focal_loss = alpha_weight * focal_weight * ce_loss
        else:
            focal_loss = focal_weight * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedReconstructionLoss(nn.Module):
    """
    🎯 加权重构损失，对异常区域给予更高权重
    """
    def __init__(self, anomaly_weight=2.0, normal_weight=1.0):
        super(WeightedReconstructionLoss, self).__init__()
        self.anomaly_weight = anomaly_weight
        self.normal_weight = normal_weight

    def forward(self, pred, target, anomaly_scores=None, mask=None):
        """
        Args:
            pred: 重构预测 (B, T, D)
            target: 重构目标 (B, T, D)
            anomaly_scores: 异常分数 (B, T) 可选
            mask: 掩码 (B, T) 可选
        """
        # 基础MSE损失
        mse_loss = F.mse_loss(pred, target, reduction='none')  # (B, T, D)

        if anomaly_scores is not None:
            # 根据异常分数调整权重
            # 异常分数越高，权重越大
            weights = self.normal_weight + (self.anomaly_weight - self.normal_weight) * anomaly_scores.unsqueeze(-1)
            weighted_loss = mse_loss * weights
        else:
            weighted_loss = mse_loss

        if mask is not None:
            # 应用掩码
            weighted_loss = weighted_loss * mask.unsqueeze(-1).float()
            return weighted_loss.sum() / (mask.sum().float() * pred.size(-1) + 1e-8)
        else:
            return weighted_loss.mean()


def enhanced_contrastive_loss(z_denoised, z_original, z_noisy, temp=0.5, lambda_align=0.05,
                            use_hard_negatives=True, margin=0.2):
    """
    🚀 增强版对比损失，解决过度对比问题

    Args:
        z_denoised: 去噪样本表示 (B, T, D)
        z_original: 原始样本表示 (B, T, D)
        z_noisy: 增噪样本表示 (B, T, D)
        temp: 温度系数（降低以增强对比）
        lambda_align: 方向对齐项权重（降低以减少过拟合）
        use_hard_negatives: 是否使用困难负样本
        margin: 边际损失的边际值
    """
    # 确保表示被L2归一化
    z_denoised = F.normalize(z_denoised, dim=-1)
    z_original = F.normalize(z_original, dim=-1)
    z_noisy = F.normalize(z_noisy, dim=-1)

    # 🎯 改进的语义对比项 - 使用边际损失而非InfoNCE
    pos_sim = F.cosine_similarity(z_original, z_denoised, dim=-1)  # (B, T)
    neg_sim = F.cosine_similarity(z_original, z_noisy, dim=-1)     # (B, T)

    # 边际损失：max(0, margin - pos_sim + neg_sim)
    margin_loss = F.relu(margin - pos_sim + neg_sim)
    semantic_loss = margin_loss.mean()

    # 🎯 简化的方向对齐项
    if lambda_align > 0:
        # 计算从增噪到去噪的向量 (理想去噪方向)
        denoising_direction = z_denoised - z_noisy  # (B, T, D)
        # 计算从增噪到原始的向量 (实际向量)
        actual_direction = z_original - z_noisy     # (B, T, D)

        # 方向对齐损失：最小化两个方向的夹角
        eps = 1e-8
        direction_similarity = F.cosine_similarity(denoising_direction, actual_direction, dim=-1, eps=eps)
        direction_alignment_loss = torch.mean(1 - direction_similarity.clamp(-1 + eps, 1 - eps))
    else:
        direction_alignment_loss = torch.tensor(0.0, device=z_original.device)

    # 总损失
    total_loss = semantic_loss + lambda_align * direction_alignment_loss

    return total_loss