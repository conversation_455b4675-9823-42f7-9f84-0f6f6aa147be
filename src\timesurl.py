import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader
import numpy as np
from models import TSEncoder
from models.TC import TC
from models.decl_loss import decl_triplet_loss
from models.losses import hierarchical_contrastive_loss
from utils import take_per_row, split_with_nan, centerize_vary_length_series, torch_pad_nan
from utils import inter_cubic_sp_torch
from utils import convert_coeff
from lib import get_unlabeled_pretrain_data
from types import SimpleNamespace


def tp_noneffect(func, x, **kwargs):
    tp = x[..., -1:]
    x = func(x[..., :-1], **kwargs)
    return torch.cat([x, tp], dim=-1)


def freq_mix(x, rate=0.5, dim=1):
    x_f = torch.fft.fft(x, dim=dim)

    m = torch.cuda.FloatTensor(x_f.shape).uniform_() < rate
    amp = abs(x_f)
    _, index = amp.sort(dim=dim, descending=True)
    dominant_mask = index > 2
    m = torch.bitwise_and(m, dominant_mask)
    freal = x_f.real.masked_fill(m, 0)
    fimag = x_f.imag.masked_fill(m, 0)

    b_idx = np.arange(x.shape[0])
    np.random.shuffle(b_idx)
    x2 = x[b_idx]
    x2_f = torch.fft.fft(x2, dim=dim)

    m = torch.bitwise_not(m)
    freal2 = x2_f.real.masked_fill(m, 0)
    fimag2 = x2_f.imag.masked_fill(m, 0)

    freal += freal2
    fimag += fimag2

    x_f = torch.complex(freal, fimag)

    x = torch.abs(torch.fft.ifft(x_f, dim=dim))
    return x


def freq_dropout(x, dropout_rate=0.5):
    x_aug = x.clone()
    x_aug_f = torch.fft.fft(x_aug)
    m = torch.cuda.FloatTensor(x_aug_f.shape).uniform_() < dropout_rate
    amp = torch.abs(x_aug_f)
    _, index = amp.sort(dim=1, descending=True)
    dominant_mask = index > 5
    m = torch.bitwise_and(m, dominant_mask)
    freal = x_aug_f.real.masked_fill(m, 0)
    fimag = x_aug_f.imag.masked_fill(m, 0)
    x_aug_f = torch.complex(freal, fimag)
    x_aug = torch.abs(torch.fft.ifft(x_aug_f, dim=1))
    return x_aug


class TimesURL:
    '''The TimesURL model'''

    def __init__(
            self,
            input_dims,
            output_dims=320,
            hidden_dims=64,
            depth=10,
            device='cuda',
            lr=0.001,
            batch_size=16,
            sgd=False,
            max_train_length=None,
            temporal_unit=0,
            after_iter_callback=None,
            after_epoch_callback=None,
            args=None
    ):
        ''' Initialize a TimesURL model.
        
        Args:
            input_dims (int): The input dimension. For a univariate time series, this should be set to 1.
            output_dims (int): The representation dimension.
            hidden_dims (int): The hidden dimension of the encoder.
            depth (int): The number of hidden residual blocks in the encoder.
            device (int): The gpu used for training and inference.
            lr (int): The learning rate.
            batch_size (int): The batch size.
            max_train_length (Union[int, NoneType]): The maximum allowed sequence length for training. For sequence with a length greater than <max_train_length>, it would be cropped into some sequences, each of which has a length less than <max_train_length>.
            temporal_unit (int): The minimum unit to perform temporal contrast. When training on a very long sequence, this param helps to reduce the cost of time and memory.
            after_iter_callback (Union[Callable, NoneType]): A callback function that would be called after each iteration.
            after_epoch_callback (Union[Callable, NoneType]): A callback function that would be called after each epoch.
        '''

        super().__init__()
        self.device = device
        self.lr = lr
        self.sgd = sgd
        self.batch_size = batch_size
        self.max_train_length = max_train_length
        self.temporal_unit = temporal_unit

        self._net = TSEncoder(input_dims=input_dims, output_dims=output_dims, hidden_dims=hidden_dims, depth=depth).to(self.device)
        
        # --- DECL Integration (回退到基线配置) ---
        from models.decl_loss import dcrad_contrastive_loss
        self.decl_loss = dcrad_contrastive_loss  # 使用原始DCRAD对比损失

        # � 设置损失权重为1:1
        self.w_rec = 1.0  # 重构损失权重
        self.w_triplet = 1.0  # 三元组损失权重
        # 完全移除分离损失权重 (w_sep)

        # 🚀 修复后的对比学习参数 - 确保正确的学习动态
        self.temp_contrast = getattr(args, 'temp_contrast', 0.7)  # 适中温度平衡学习效果
        self.lambda_align = getattr(args, 'lambda_align', 0.2)    # 适中方向对齐权重
        self.reconstruct_target = getattr(args, 'reconstruct_target', 'original')  # 'original' or 'denoised'

        # 移除所有可能有问题的"优化"组件
        self.enhanced_denoiser = None
        self.signal_quality_assessor = None
        self.focal_loss = None
        self.weighted_recon_loss = None
        self.use_focal_loss = False
        # --- End DECL Integration ---

        self.net = torch.optim.swa_utils.AveragedModel(self._net)
        self.net.update_parameters(self._net)

        self.after_iter_callback = after_iter_callback
        self.after_epoch_callback = after_epoch_callback
        self.args = args

        self.n_epochs = 0
        self.n_iters = 0

        # 🔧 损失平滑机制
        self.loss_history = {'rec': [], 'triplet': [], 'total': []}
        self.loss_smooth_window = 5  # 平滑窗口大小

        # 🔧 梯度监控
        self.gradient_history = {'rec': [], 'triplet': []}
        self.monitor_gradients = True

    def _smooth_loss(self, current_loss, loss_type='total'):
        """损失平滑函数 - 使用指数移动平均"""
        if loss_type not in self.loss_history:
            self.loss_history[loss_type] = []

        self.loss_history[loss_type].append(current_loss)

        # 保持窗口大小
        if len(self.loss_history[loss_type]) > self.loss_smooth_window:
            self.loss_history[loss_type].pop(0)

        # 计算指数移动平均
        if len(self.loss_history[loss_type]) == 1:
            return current_loss
        else:
            alpha = 0.3  # 平滑系数
            history = self.loss_history[loss_type]
            smoothed = history[0]
            for loss_val in history[1:]:
                smoothed = alpha * loss_val + (1 - alpha) * smoothed
            return smoothed

    def _monitor_gradients(self, loss_rec, loss_triplet):
        """监控梯度范数以诊断训练问题"""
        if not self.monitor_gradients:
            return

        # 计算重构损失的梯度范数
        rec_grads = torch.autograd.grad(loss_rec, self._net.parameters(),
                                       retain_graph=True, create_graph=False)
        rec_grad_norm = torch.sqrt(sum(torch.sum(g**2) for g in rec_grads if g is not None))

        # 计算三元组损失的梯度范数
        triplet_grads = torch.autograd.grad(loss_triplet, self._net.parameters(),
                                          retain_graph=True, create_graph=False)
        triplet_grad_norm = torch.sqrt(sum(torch.sum(g**2) for g in triplet_grads if g is not None))

        # 记录梯度历史
        self.gradient_history['rec'].append(rec_grad_norm.item())
        self.gradient_history['triplet'].append(triplet_grad_norm.item())

        # 保持历史长度
        if len(self.gradient_history['rec']) > 20:
            self.gradient_history['rec'].pop(0)
            self.gradient_history['triplet'].pop(0)

        return rec_grad_norm.item(), triplet_grad_norm.item()

    def fit(self, train_data, n_epochs=None, n_iters=None, verbose=False, is_scheduler=True, temp=1.0):
        ''' Training the TimesURL model.
        
        Args:
            train_data (numpy.ndarray): The training data. It should have a shape of (n_instance, n_timestamps, n_features). All missing data should be set to NaN.
            n_epochs (Union[int, NoneType]): The number of epochs. When this reaches, the training stops.
            n_iters (Union[int, NoneType]): The number of iterations. When this reaches, the training stops. If both n_epochs and n_iters are not specified, a default setting would be used that sets n_iters to 200 for a dataset with size <= 100000, 600 otherwise.
            verbose (bool): Whether to print the training loss after each epoch.
            
        Returns:
            loss_log: a list containing the training losses on each epoch.
        '''
        print(f"\n🔍 === TRAINING FLOW INVESTIGATION ===")
        print(f"📊 Input train_data type: {type(train_data)}")
        print(f"📊 Input train_data keys: {train_data.keys() if isinstance(train_data, dict) else 'Not a dict'}")
        
        # --- DECL Integration: Custom data handling ---
        # Check if we are in DECL mode by looking for specific keys in train_data
        is_decl_mode = 'x_denoised' in train_data and 'x_noisy' in train_data
        print(f"🎯 DECL Mode Detected: {is_decl_mode}")
        
        if is_decl_mode:
            print(f"🔄 === DECL TRAINING BRANCH ===")
            train_x = train_data['x']
            train_x_denoised = train_data['x_denoised']
            train_x_noisy = train_data['x_noisy']
            train_mask = train_data['mask']
            
            print(f"📈 Original data shape: {train_x.shape}")
            print(f"📈 Denoised data shape: {train_x_denoised.shape}")
            print(f"📈 Noisy data shape: {train_x_noisy.shape}")
            print(f"📈 Mask shape: {train_mask.shape}")
            print(f"📈 Batch size: {self.batch_size}")
            
            # 🚀 初始化门控网络自适应去噪系统
            if not hasattr(self, 'adaptive_denoising'):
                try:
                    from models.learnable_gating import integrate_gating_with_timesurl
                except (ImportError, ValueError):
                    from src.models.learnable_gating import integrate_gating_with_timesurl
                print(f"🎯 初始化Gumbel-Softmax门控网络...")
                self.adaptive_denoising = integrate_gating_with_timesurl(self, train_data, device=self.device)
                print(f"✅ 门控网络初始化完成")
                print(f"🌡️ 温度退火设置: 初始{self.adaptive_denoising.gating_network.initial_temperature} -> 最终{self.adaptive_denoising.gating_network.final_temperature}")
            
            # 使用原始数据创建数据集（不再使用预生成的去噪数据）
            train_dataset = TensorDataset(
                torch.from_numpy(train_x).to(torch.float),
                torch.from_numpy(train_x_noisy).to(torch.float),
                torch.from_numpy(train_mask).to(torch.float)
            )
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            
            print(f"📊 DataLoader created with {len(train_loader)} batches")
            print(f"📊 Expected batches per epoch: {len(train_x) // self.batch_size + (1 if len(train_x) % self.batch_size > 0 else 0)}")
            
            # DECL-specific optimizer (包含门控网络参数)
            params = list(self._net.parameters()) + list(self.adaptive_denoising.parameters())

            # 🔧 降低学习率和权重衰减以提升稳定性
            stable_lr = self.lr * 0.5  # 降低学习率到原来的一半
            stable_weight_decay = 1e-4  # 降低权重衰减

            if self.sgd:
                optimizer = torch.optim.SGD(params, lr=stable_lr, weight_decay=stable_weight_decay, momentum=0.9)
            else:
                optimizer = torch.optim.AdamW(params, lr=stable_lr, weight_decay=stable_weight_decay)
            print(f"🔧 DECL Optimizer created with {len(params)} parameters (包含门控网络)")
        else:
            print(f"🔄 === ORIGINAL TIMESURL TRAINING BRANCH ===")
            # Original TimesURL data handling
            train_data, mask = train_data['x'], train_data['mask']
            print(f"📈 Original TimesURL data shape: {train_data.shape}")
            print(f"📈 Original TimesURL mask shape: {mask.shape}")
            
            assert train_data.ndim == 3
            if self.max_train_length is not None:
                sections = train_data.shape[1] // self.max_train_length
                if sections >= 2:
                    train_data = np.concatenate(split_with_nan(train_data, sections, axis=1), axis=0)
                    mask = np.concatenate(split_with_nan(mask, sections, axis=1), axis=0)
                    print(f"📈 After max_train_length split: {train_data.shape}")

            temporal_missing = np.isnan(train_data).all(axis=-1).any(axis=0)
            if temporal_missing[0] or temporal_missing[-1]:
                train_data, mask = centerize_vary_length_series(train_data, mask)
                print(f"📈 After centerize_vary_length_series: {train_data.shape}")

            mask = mask[~np.isnan(train_data[..., :-1]).all(axis=2).all(axis=1)]
            train_data = train_data[~np.isnan(train_data[..., :-1]).all(axis=2).all(axis=1)]
            mask[np.isnan(mask)] = 0
            print(f"📈 After NaN filtering: {train_data.shape}")
            print(f"🔍 NaN filtering removed {len(train_data) - train_data.shape[0]} samples")
            
            x, t = train_data[..., :-1], train_data[..., -1:]
            print(f"📈 Features x shape: {x.shape}")
            print(f"📈 Time t shape: {t.shape}")
            
            obj = get_unlabeled_pretrain_data(np.concatenate([x, mask, t], axis=-1), self.args)
            train_loader = obj['train_dataloader']
            print(f"📊 Original TimesURL DataLoader created with {len(train_loader)} batches")
            
            if self.sgd:
                optimizer = torch.optim.SGD(self._net.parameters(), lr=self.lr, weight_decay=5e-4, momentum=0.9)
            else:
                optimizer = torch.optim.AdamW(self._net.parameters(), lr=self.lr, weight_decay=5e-4)

        # 🔍 Training parameter investigation
        print(f"\n🎯 === TRAINING PARAMETERS INVESTIGATION ===")
        print(f"📊 Input n_epochs: {n_epochs}")
        print(f"📊 Input n_iters: {n_iters}")
        
        if n_iters is None and n_epochs is None:
            if is_decl_mode:
                data_size = len(train_x)
            else:
                data_size = train_data.size if hasattr(train_data, 'size') else len(train_data)
            n_iters = 200 if data_size <= 100000 else 600
            print(f"🔧 Default n_iters set to: {n_iters} (data_size: {data_size})")

        if self.lr <= 1e-5 and n_iters is not None:
            old_n_iters = n_iters
            n_iters *= 1.2
            print(f"🔧 Low LR adjustment: n_iters {old_n_iters} -> {n_iters}")

        # 计算最大轮次（用于温度退火）
        if n_epochs is not None:
            max_epochs = n_epochs
        elif n_iters is not None:
            max_epochs = n_iters // len(train_loader) + 1
        else:
            max_epochs = 100  # 默认值

        print(f"📊 Final training parameters:")
        print(f"   - n_epochs: {n_epochs}")
        print(f"   - n_iters: {n_iters}")
        print(f"   - max_epochs: {max_epochs}")
        print(f"   - batches_per_epoch: {len(train_loader)}")
        print(f"   - self.n_epochs: {self.n_epochs}")
        print(f"   - self.n_iters: {self.n_iters}")

        if is_scheduler:
            if n_iters is not None and n_epochs is None:
                scheduler_max_epochs = n_iters // len(train_loader)
                print(f"🔧 Scheduler max_epochs calculated: {scheduler_max_epochs} = {n_iters} // {len(train_loader)}")
            else:
                scheduler_max_epochs = n_epochs
                print(f"🔧 Scheduler max_epochs set to: {scheduler_max_epochs}")
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, scheduler_max_epochs)

        loss_log = []
        
        print(f"\n🚀 === STARTING TRAINING LOOP ===")

        # 🔍 打印损失权重配置以验证分离损失已移除
        print(f"\n📊 === 损失函数配置 ===")
        print(f"   🔧 重构损失权重 (w_rec): {self.w_rec}")
        print(f"   🔧 三元组损失权重 (w_triplet): {self.w_triplet}")
        print(f"   ✅ 分离损失权重 (w_sep): 已完全移除")
        print(f"   📝 总损失公式: Total = {self.w_rec} * Reconstruction + {self.w_triplet} * Triplet")
        print(f"   🌡️ 对比温度 (temp_contrast): {self.temp_contrast}")
        print(f"   🎯 方向对齐权重 (lambda_align): {self.lambda_align}")

        import time
        training_start_time = time.time()

        while True:
            epoch_start_time = time.time()
            print(f"\n📅 Starting Epoch {self.n_epochs}")
            print(f"🔍 Check termination: n_epochs={n_epochs}, self.n_epochs={self.n_epochs}")
            
            if n_epochs is not None and self.n_epochs >= n_epochs:
                print(f"🛑 Breaking due to epoch limit: {self.n_epochs} >= {n_epochs}")
                break

            cum_loss = 0
            n_epoch_iters = 0

            interrupted = False
            
            if is_decl_mode:
                # --- DECL Training Loop ---
                print(f"🔄 === DECL TRAINING LOOP (Epoch {self.n_epochs}) ===")

                # 🔍 每个epoch开始时验证损失配置
                if self.n_epochs == 0:  # 只在第一个epoch打印详细信息
                    print(f"   🔍 === 损失组件验证 ===")
                    print(f"      ✅ 重构损失: 启用 (权重={self.w_rec})")
                    print(f"      ✅ 三元组损失: 启用 (权重={self.w_triplet})")
                    print(f"      ❌ 分离损失: 已移除 (不存在w_sep属性)")
                    print(f"      📝 预期损失计算: loss = {self.w_rec} * loss_rec + {self.w_triplet} * loss_triplet")

                    # 验证w_sep属性确实不存在
                    if hasattr(self, 'w_sep'):
                        print(f"      ⚠️ 警告: w_sep属性仍然存在，值为{self.w_sep}")
                    else:
                        print(f"      ✅ 确认: w_sep属性已完全移除")

                self.adaptive_denoising.train()
                batch_count = 0
                for x_batch, x_noisy_batch, mask_batch in train_loader:
                    batch_start_time = time.time()
                    batch_count += 1
                    
                    # 只在每20个batch或最后一个batch打印进度
                    if batch_count % 20 == 0 or batch_count == len(train_loader):
                        print(f"🔄 Batch {batch_count}/{len(train_loader)}")
                    if n_iters is not None and self.n_iters >= n_iters:
                        print(f"🛑 Breaking due to iteration limit: {self.n_iters} >= {n_iters}")
                        interrupted = True
                        break
                    
                    x_batch = x_batch.to(self.device)
                    x_noisy_batch = x_noisy_batch.to(self.device)
                    mask_batch = mask_batch.to(self.device)
                    
                    optimizer.zero_grad()
                    
                    # 🎯 Step 1: 使用门控网络动态生成去噪数据（按论文描述）
                    # 排除时间位置特征进行去噪
                    x_features_only = x_batch[..., :-1]  # 排除最后一维时间特征

                    # 按论文描述：传递当前epoch进行温度退火
                    x_denoised_features, gumbel_weights, gate_logits = self.adaptive_denoising(
                        x_features_only, epoch=self.n_epochs, max_epochs=max_epochs
                    )

                    # 移除增强去噪器相关代码，直接使用原始去噪结果

                    # 重新添加时间位置特征
                    x_denoised_batch = torch.cat([x_denoised_features, x_batch[..., -1:]], dim=-1)
                    
                    # Step 2: Reconstruction Loss (Patch-level as per user request)
                    # Get patch parameters from args, with reasonable defaults
                    patch_len = getattr(self.args, 'patch_len', 16)
                    patch_mask_ratio = getattr(self.args, 'patch_mask_ratio', 0.4)
                    
                    x_masked = x_batch.clone()
                    B, T, C = x_batch.shape
                    
                    if T >= patch_len:
                        num_patches = T // patch_len
                        n_mask_patches = int(num_patches * patch_mask_ratio)

                        # Generate a random mask for each sample in the batch
                        # noise shape: (B, num_patches)
                        noise = torch.rand(B, num_patches, device=self.device)
                        # ids_shuffle shape: (B, num_patches)
                        ids_shuffle = torch.argsort(noise, dim=1)
                        # ids_mask shape: (B, n_mask_patches)
                        ids_mask = ids_shuffle[:, :n_mask_patches]

                        # Create a boolean mask for patches, shape: (B, num_patches)
                        patch_mask = torch.zeros(B, num_patches, dtype=torch.bool, device=self.device)
                        patch_mask.scatter_(1, ids_mask, True)

                        # Expand patch_mask to time_mask, ensuring correct length
                        # First expand to patch-based mask
                        time_mask_patches = patch_mask.repeat_interleave(patch_len, dim=1)  # (B, num_patches * patch_len)
                        
                        # Create full time mask with correct length T
                        time_mask = torch.zeros(B, T, dtype=torch.bool, device=self.device)
                        # Copy the patch-based mask to the beginning
                        time_mask[:, :time_mask_patches.size(1)] = time_mask_patches
                        # For remaining timesteps (if T not divisible by patch_len), leave as False (unmasked)

                        # Apply mask by setting masked elements to 0. Shape: (B, T, 1) -> (B, T, C)
                        x_masked = x_masked * (~time_mask.unsqueeze(-1)).float()

                        # The mask for TSEncoder should indicate valid steps, not what's masked for recon.
                        # Here we use the original mask from the dataloader.
                        features_masked, x_recon = self._net({'data': x_masked, 'mask': mask_batch, 'mask_origin': mask_batch})
                        
                        # Calculate reconstruction loss ONLY on the masked patches
                        # 🧪 实验性功能：可选择重建目标（原始 vs 去噪）
                        if self.reconstruct_target == 'denoised':
                            target = x_denoised_batch[..., :-1]  # 重建去噪信号
                            # 修复NaN问题：检查并清理去噪数据
                            if torch.isnan(target).any():
                                # 如果去噪数据包含NaN，回退到原始数据
                                target = x_batch[..., :-1]
                        else:
                            target = x_batch[..., :-1]  # 重建原始信号（默认）
                        
                        # 确保目标数据不包含NaN或无穷值
                        target = torch.where(torch.isnan(target), torch.zeros_like(target), target)
                        target = torch.where(torch.isinf(target), torch.zeros_like(target), target)
                        # time_mask must be expanded to match the feature dimension for indexing
                        loss_mask = time_mask.unsqueeze(-1).expand_as(target)
                        
                        # -- 修复：正确计算每个样本的重建误差 --
                        # 计算完整的每元素误差，保持原始维度
                        full_element_loss = F.mse_loss(x_recon, target, reduction='none')

                        # 只在掩码为True的地方计算损失
                        masked_loss = full_element_loss * loss_mask.float()

                        # 计算每个样本的总误差和掩码元素数量
                        per_sample_loss_sum = masked_loss.sum(dim=(1, 2))  # (B,)
                        masked_elements_per_sample = loss_mask.sum(dim=(1, 2))  # (B,)

                        # 避免除以零
                        masked_elements_per_sample = torch.clamp(masked_elements_per_sample, min=1)

                        # 计算每个样本的平均重建误差
                        per_sample_loss = per_sample_loss_sum / masked_elements_per_sample.float()

                        # 批次的平均重建损失（用于反向传播）
                        loss_rec = per_sample_loss.mean()

                    else:
                        # Fallback for sequences shorter than patch_len: no reconstruction loss
                        features_masked, x_recon = self._net({'data': x_masked, 'mask': mask_batch, 'mask_origin': mask_batch})
                        loss_rec = torch.tensor(0.0, device=self.device)


                    # Step 3: DECL Losses (仅包含三元组损失)
                    # Use a non-masking call for feature extraction
                    # Create a dummy mask of all ones for unmasked feature extraction
                    full_mask = torch.ones_like(mask_batch)

                    # 🔧 优化特征提取 - 减少过度归一化
                    z_orig_raw = self._net({'data': x_batch, 'mask': full_mask, 'mask_origin': full_mask})[0]
                    z_denoised_raw = self._net({'data': x_denoised_batch, 'mask': full_mask, 'mask_origin': full_mask})[0]
                    z_noisy_raw = self._net({'data': x_noisy_batch, 'mask': full_mask, 'mask_origin': full_mask})[0]

                    # 使用更温和的归一化策略 - 只在损失计算时归一化
                    z_orig = z_orig_raw
                    z_denoised = z_denoised_raw
                    z_noisy = z_noisy_raw

                    # DCRAD完整对比损失 (语义对比 + 方向对齐)
                    loss_triplet = self.decl_loss(
                        z_denoised, z_orig, z_noisy, 
                        temp=self.temp_contrast, 
                        lambda_align=self.lambda_align
                    )

                    # --- 分离损失已移除 ---

                    # Step 4: Total Loss (TR + CL) - 确认只包含重构损失和三元组损失
                    loss = self.w_rec * loss_rec + self.w_triplet * loss_triplet

                    # 🔧 应用损失平滑
                    smoothed_rec = self._smooth_loss(loss_rec.item(), 'rec')
                    smoothed_triplet = self._smooth_loss(loss_triplet.item(), 'triplet')
                    smoothed_total = self._smooth_loss(loss.item(), 'total')

                    # 🔧 梯度监控 (每10个batch监控一次)
                    if batch_count % 10 == 0 and self.monitor_gradients:
                        try:
                            rec_grad_norm, triplet_grad_norm = self._monitor_gradients(loss_rec, loss_triplet)
                            # 如果三元组梯度过小，说明学习停滞
                            if triplet_grad_norm < 1e-6:
                                print(f"   ⚠️  Warning: Triplet gradient too small ({triplet_grad_norm:.2e})")
                        except Exception:
                            pass  # 梯度监控失败时不影响训练

                    # 简化损失打印 - 显示平滑后的损失
                    if batch_count % 5 == 0:
                        print(f"   Batch {batch_count}/{len(train_loader)}: Total={smoothed_total:.4f} (Rec={smoothed_rec:.4f}, Trip={smoothed_triplet:.4f})")

                    loss.backward()

                    # 🔧 梯度裁剪 - 防止梯度爆炸
                    torch.nn.utils.clip_grad_norm_(self._net.parameters(), max_norm=1.0)
                    if hasattr(self, 'adaptive_denoising') and self.adaptive_denoising is not None:
                        torch.nn.utils.clip_grad_norm_(self.adaptive_denoising.parameters(), max_norm=1.0)

                    optimizer.step()

                    optimizer.step()
                    self.net.update_parameters(self._net)

                    cum_loss += loss.item()
                    n_epoch_iters += 1
                    self.n_iters += 1

                    # 移除损失历史记录（早停相关）
                    
                    if self.after_iter_callback is not None:
                        self.after_iter_callback(self, loss.item())
                    
                    # 只在每40个batch显示简化统计
                    if batch_count % 40 == 0:
                        current_temp = self.adaptive_denoising.gating_network.current_temperature
                        avg_loss = cum_loss / n_epoch_iters
                        print(f"   📊 Batch {batch_count}: Temp={current_temp:.3f}, AvgLoss={avg_loss:.4f}")
                
                # DECL Epoch completed

            else:
                # --- Original TimesURL Training Loop ---
                print(f"🔄 === ORIGINAL TIMESURL TRAINING LOOP (Epoch {self.n_epochs}) ===")

                # 🔍 在非DECL模式中也添加损失组件验证
                if self.n_epochs == 0:  # 只在第一个epoch打印详细信息
                    print(f"   🔍 === 非DECL模式损失组件验证 ===")
                    print(f"      ✅ 重构损失: 启用 (权重={self.w_rec})")
                    print(f"      ✅ 三元组损失: 启用 (权重={self.w_triplet})")
                    print(f"      ❌ 分离损失: 已移除 (不存在w_sep属性)")
                    print(f"      📝 预期损失计算: loss = {self.w_rec} * loss_rec + {self.w_triplet} * loss_triplet")

                    # 验证w_sep属性确实不存在
                    if hasattr(self, 'w_sep'):
                        print(f"      ⚠️ 警告: w_sep属性仍然存在，值为{self.w_sep}")
                    else:
                        print(f"      ✅ 确认: w_sep属性已完全移除")

                batch_count = 0
                for batch in train_loader:
                    batch_count += 1
                    
                    if batch_count % 20 == 0 or batch_count == len(train_loader):
                        print(f"🔄 Batch {batch_count}/{len(train_loader)}")
                    
                    if n_iters is not None and self.n_iters >= n_iters:
                        print(f"🛑 Breaking due to iteration limit: {self.n_iters} >= {n_iters}")
                        interrupted = True
                        break

                    value = batch['value'].to(self.device)
                    time_batch = batch['time'].to(self.device)
                    mask = batch['mask'].to(self.device)
                    mask_origin = batch['mask_origin'].to(self.device)

                    optimizer.zero_grad()

                    loss = torch.tensor([0.]).to(self.device)
                    for seq in range(value.size(1)):
                        x, t, m, m_old = value[:, seq], time_batch[:, seq], mask[:, seq], mask_origin[:, seq]
                        dim = x.size(-1)
                        x = torch.cat([x, t.unsqueeze(2)], dim=-1)

                        ts_l = x.size(1)
                        crop_l = np.random.randint(low=2 ** (self.temporal_unit + 1), high=ts_l + 1)
                        crop_left = np.random.randint(ts_l - crop_l + 1)
                        crop_right = crop_left + crop_l
                        crop_eleft = np.random.randint(crop_left + 1)
                        crop_eright = np.random.randint(low=crop_right, high=ts_l + 1)
                        crop_offset = np.random.randint(low=-crop_eleft, high=ts_l - crop_eright + 1, size=x.size(0))

                        x_left = take_per_row(x, crop_offset + crop_eleft, crop_right - crop_eleft)
                        x_right = tp_noneffect(freq_mix, take_per_row(x, crop_offset + crop_left, crop_eright - crop_left), rate=0.5)

                        mask1 = take_per_row(m[..., :dim], crop_offset + crop_eleft, crop_right - crop_eleft)
                        mask2 = take_per_row(m[..., :dim], crop_offset + crop_left, crop_eright - crop_left)

                        mask1_inter = take_per_row(m[..., dim:], crop_offset + crop_eleft, crop_right - crop_eleft)
                        mask2_inter = take_per_row(m[..., dim:], crop_offset + crop_left, crop_eright - crop_left)

                        mask1_origin = take_per_row(m_old, crop_offset + crop_eleft, crop_right - crop_eleft)
                        mask2_origin = take_per_row(m_old, crop_offset + crop_left, crop_eright - crop_left)

                        out1, left_recon = self._net({'data': x_left, 'mask': mask1, 'mask_inter': mask1_inter, 'mask_origin': mask1_origin})
                        out2, right_recon = self._net({'data': x_right, 'mask': mask2, 'mask_inter': mask2_inter, 'mask_origin': mask2_origin})

                        out1, left_recon = out1[:, -crop_l:], left_recon[:, -crop_l:]
                        out2, right_recon = out2[:, :crop_l], right_recon[:, :crop_l]

                        x_left, x_right = x_left[:, -crop_l:], x_right[:, :crop_l]

                        mask1, mask2 = mask1[:, -crop_l:], mask2[:, :crop_l]
                        mask1_inter, mask2_inter = mask1_inter[:, -crop_l:], mask2_inter[:, :crop_l]

                        # 🔍 计算对比损失
                        contrastive_loss = hierarchical_contrastive_loss(
                            out1,
                            out2,
                            temporal_unit=self.temporal_unit,
                            temp=temp
                        )
                        weighted_contrastive_loss = self.args.lmd * contrastive_loss
                        loss += weighted_contrastive_loss

                        # 🔍 计算重构损失
                        recon_loss_left = torch.tensor(0.0, device=self.device)
                        recon_loss_right = torch.tensor(0.0, device=self.device)

                        if torch.sum(mask1_inter) > 0:
                            recon_loss_left = 1 * torch.sum(torch.pow((x_left[..., :-1] - left_recon) * mask1_inter, 2)) / (
                                    torch.sum(mask1_inter) + 1e-10) / 2
                            loss += recon_loss_left

                        if torch.sum(mask2_inter) > 0:
                            recon_loss_right = 1 * torch.sum(torch.pow((x_right[..., :-1] - right_recon) * mask2_inter, 2)) / (
                                    torch.sum(mask2_inter) + 1e-10) / 2
                            loss += recon_loss_right

                    # 简化损失打印 - 非DECL模式
                    if batch_count % 5 == 0:
                        total_recon_loss = recon_loss_left + recon_loss_right
                        print(f"   Batch {batch_count}/{len(train_loader)}: Total={loss.item():.4f} (Contrast={weighted_contrastive_loss.item():.4f}, Recon={total_recon_loss.item():.4f})")

                    loss.requires_grad_(True)
                    backward_start = time.time()
                    loss.backward()
                    optimizer.step()
                    self.net.update_parameters(self._net)
                    print(f"   ⏱️ Backward pass: {time.time() - backward_start:.3f}s")

                    cum_loss += loss.item()
                    n_epoch_iters += 1
                    self.n_iters += 1
                    
                    batch_time = time.time() - batch_start_time
                    print(f"   ⏱️ Total batch time: {batch_time:.3f}s")
                    print(f"   📈 Cumulative loss: {cum_loss:.6f}, iters: {n_epoch_iters}")
                    
                    if self.after_iter_callback is not None:
                        self.after_iter_callback(self, loss.item())
                
                # Original TimesURL Epoch completed

            cum_loss /= n_epoch_iters if n_epoch_iters else 1
            loss_log.append(cum_loss)
            
            epoch_time = time.time() - epoch_start_time
            total_time_so_far = time.time() - training_start_time
            
            # 简化epoch完成信息
            print(f"Epoch #{self.n_epochs}: loss={cum_loss:.6f}")
            self.n_epochs += 1
            
            # Incremented epoch counter
            
            if is_scheduler:
                old_lr = optimizer.param_groups[0]['lr']
                scheduler.step()
                new_lr = optimizer.param_groups[0]['lr']
                # Scheduler step applied

            if self.after_epoch_callback is not None:
                self.after_epoch_callback(self, cum_loss)

            # 移除早停机制

            if interrupted:
                print(f"🛑 === TRAINING INTERRUPTED ===")
                print(f"   🔍 Reason: n_iters limit reached ({self.n_iters} >= {n_iters})")
                break
        
        total_training_time = time.time() - training_start_time
        print(f"\nTraining time: {total_training_time:.1f}s")
        
        # end

        return loss_log

    def fit_with_decl(self, train_data, n_epochs=None, n_iters=None, verbose=False):
        """
        使用DECL方法训练TimesURL模型
        """
        from denoising_utils import generate_decl_training_data
        
        # 生成DECL训练数据
        decl_data = generate_decl_training_data(train_data['x'], train_data['mask'])
        
        # 调用原有的fit方法
        return self.fit(decl_data, n_epochs, n_iters, verbose)

    def fit_with_detailed_monitoring(self, train_data, n_epochs=None, n_iters=None, verbose=False):
        """带详细监控的训练方法"""
        
        # 在原有fit方法基础上添加监控
        monitoring_data = {
            'batch_times': [],
            'forward_times': [],
            'backward_times': [],
            'gradient_norms': [],
            'parameter_changes': [],
            'representation_changes': []
        }
        
        # 保存初始参数状态
        initial_params = {name: param.clone().detach() for name, param in self._net.named_parameters()}
        
        # 在训练循环中添加监控代码
        for epoch in range(n_epochs):
            epoch_start = time.time()
            
            for batch_idx, batch in enumerate(train_loader):
                batch_start = time.time()
                
                # ... 原有训练代码 ...
                
                # 监控梯度范数
                total_grad_norm = 0
                for param in self._net.parameters():
                    if param.grad is not None:
                        total_grad_norm += param.grad.data.norm(2).item() ** 2
                total_grad_norm = total_grad_norm ** 0.5
                monitoring_data['gradient_norms'].append(total_grad_norm)
                
                # 监控参数变化
                param_change = 0
                for name, param in self._net.named_parameters():
                    if name in initial_params:
                        change = (param - initial_params[name]).norm().item()
                        param_change += change
                monitoring_data['parameter_changes'].append(param_change)
                
                batch_time = time.time() - batch_start
                monitoring_data['batch_times'].append(batch_time)
                
                # 详细时间分析
                if batch_time < 0.01:  # 如果batch时间异常短
                    print(f"⚠️ 警告: Batch {batch_idx} 处理时间异常短: {batch_time:.6f}s")
                    print(f"   数据形状: {batch[0].shape}")
                    print(f"   梯度范数: {total_grad_norm:.6f}")
                    print(f"   参数变化: {param_change:.6f}")
        
        # 分析监控结果
        self._analyze_training_monitoring(monitoring_data)
        
        return loss_log

    def _analyze_training_monitoring(self, monitoring_data):
        """分析训练监控数据"""
        
        print("\n=== 训练过程分析 ===")
        
        avg_batch_time = np.mean(monitoring_data['batch_times'])
        avg_grad_norm = np.mean(monitoring_data['gradient_norms'])
        total_param_change = monitoring_data['parameter_changes'][-1] if monitoring_data['parameter_changes'] else 0
        
        print(f"平均batch处理时间: {avg_batch_time:.4f}s")
        print(f"平均梯度范数: {avg_grad_norm:.6f}")
        print(f"总参数变化量: {total_param_change:.6f}")
        
        # 异常检测
        if avg_batch_time < 0.01:
            print("⚠️ 警告: 训练速度异常快，可能存在问题")
        
        if avg_grad_norm < 1e-6:
            print("⚠️ 警告: 梯度范数过小，可能存在梯度消失")
        elif avg_grad_norm > 10:
            print("⚠️ 警告: 梯度范数过大，可能存在梯度爆炸")
        
        if total_param_change < 1e-4:
            print("⚠️ 警告: 参数变化很小，模型可能没有有效学习")

    def _eval_with_pooling(self, x, mask=None, slicing=None, encoding_window=None):
        out = self.net(x.to(self.device, non_blocking=True), mask)
        if encoding_window == 'full_series':
            if slicing is not None:
                out = out[:, slicing]
            out = F.max_pool1d(
                out.transpose(1, 2),
                kernel_size=out.size(1),
            ).transpose(1, 2)

        elif isinstance(encoding_window, int):
            out = F.max_pool1d(
                out.transpose(1, 2),
                kernel_size=encoding_window,
                stride=1,
                padding=encoding_window // 2
            ).transpose(1, 2)
            if encoding_window % 2 == 0:
                out = out[:, :-1]
            if slicing is not None:
                out = out[:, slicing]

        elif encoding_window == 'multiscale':
            p = 0
            reprs = []
            while (1 << p) + 1 < out.size(1):
                t_out = F.max_pool1d(
                    out.transpose(1, 2),
                    kernel_size=(1 << (p + 1)) + 1,
                    stride=1,
                    padding=1 << p
                ).transpose(1, 2)
                if slicing is not None:
                    t_out = t_out[:, slicing]
                reprs.append(t_out)
                p += 1
            out = torch.cat(reprs, dim=-1)

        else:
            if slicing is not None:
                out = out[:, slicing]

        return out.cpu()

    def encode(self, data, mask=None, encoding_window=None, casual=False, sliding_length=None, sliding_padding=0,
               batch_size=None):
        ''' Compute representations using the model.

        Args:
            data (numpy.ndarray): This should have a shape of (n_instance, n_timestamps, n_features). All missing data should be set to NaN.
            mask (str): The mask used by encoder can be specified with this parameter. This can be set to 'binomial', 'continuous', 'all_true', 'all_false' or 'mask_last'.
            encoding_window (Union[str, int]): When this param is specified, the computed representation would the max pooling over this window. This can be set to 'full_series', 'multiscale' or an integer specifying the pooling kernel size.
            casual (bool): When this param is set to True, the future informations would not be encoded into representation of each timestamp.
            sliding_length (Union[int, NoneType]): The length of sliding window. When this param is specified, a sliding inference would be applied on the time series.
            sliding_padding (int): This param specifies the contextual data length used for inference every sliding windows.
            batch_size (Union[int, NoneType]): The batch size used for inference. If not specified, this would be the same batch size as training.

        Returns:
            repr: The representations for data.
        '''
        assert self.net is not None, 'please train or load a net first'
        assert isinstance(data, dict) or data.ndim == 3
        if batch_size is None:
            batch_size = self.batch_size
        n_samples, ts_l, _ = data.shape if not isinstance(data, dict) else data['x'].shape

        org_training = self.net.training
        self.net.eval()

        if isinstance(data, dict):
            data = np.concatenate((data['x'], data['mask']), axis=-1)
        dataset = TensorDataset(torch.from_numpy(data).to(torch.float))
        loader = DataLoader(dataset, batch_size=batch_size)

        with torch.no_grad():
            output = []
            for batch in loader:
                x = batch[0]
                if sliding_length is not None:
                    reprs = []
                    if n_samples < batch_size:
                        calc_buffer = []
                        calc_buffer_l = 0
                    for i in range(0, ts_l, sliding_length):
                        l = i - sliding_padding
                        r = i + sliding_length + (sliding_padding if not casual else 0)
                        x_sliding = torch_pad_nan(
                            x[:, max(l, 0): min(r, ts_l)],
                            left=-l if l < 0 else 0,
                            right=r - ts_l if r > ts_l else 0,
                            dim=1
                        )
                        if n_samples < batch_size:
                            if calc_buffer_l + n_samples > batch_size:
                                # 获取编码器输出
                                encoder_out = self._eval_with_pooling(
                                    torch.cat(calc_buffer, dim=0),
                                    mask,
                                    slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                    encoding_window=encoding_window
                                )
                                # 直接使用编码器输出（去掉projection_head）
                                if encoder_out.dim() == 3:
                                    # 使用全局平均池化将时间维度压缩
                                    encoder_out = encoder_out.mean(dim=1)  # (batch, features)
                                    
                                reprs += torch.split(encoder_out, n_samples)
                                calc_buffer = []
                                calc_buffer_l = 0
                            calc_buffer.append(x_sliding)
                            calc_buffer_l += n_samples
                        else:
                            encoder_out = self._eval_with_pooling(
                                x_sliding,
                                mask,
                                slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                encoding_window=encoding_window
                            )
                            if encoder_out.dim() == 3:
                                encoder_out = encoder_out.mean(dim=1)
                            reprs.append(encoder_out)

                    if n_samples < batch_size:
                        if calc_buffer_l > 0:
                            encoder_out = self._eval_with_pooling(
                                torch.cat(calc_buffer, dim=0),
                                mask,
                                slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                encoding_window=encoding_window
                            )
                            if encoder_out.dim() == 3:
                                encoder_out = encoder_out.mean(dim=1)
                            reprs += torch.split(encoder_out, n_samples)
                            calc_buffer = []
                            calc_buffer_l = 0

                    out = torch.cat(reprs, dim=0)
                else:
                    encoder_out = self._eval_with_pooling(x, mask, encoding_window=encoding_window)
                    if encoding_window == 'full_series':
                        encoder_out = encoder_out.squeeze(1)
                    
                    # 处理时间维度
                    if encoder_out.dim() == 3:
                        encoder_out = encoder_out.mean(dim=1)  # 全局平均池化
                    
                    # 直接使用编码器输出（去掉projection_head）
                    out = encoder_out

                output.append(out)

            output = torch.cat(output, dim=0)

        self.net.train(org_training)
        return output.numpy()

    def encode_with_projection_head(self, data, mask=None, encoding_window=None, casual=False, 
                                   sliding_length=None, sliding_padding=0, batch_size=None):
        """
        使用编码器和投影头进行编码，返回投影头的输出。
        
        Args:
            data: 输入数据
            mask: 掩码设置
            encoding_window: 编码窗口设置
            casual: 是否使用因果模式
            sliding_length: 滑动窗口长度
            sliding_padding: 滑动填充
            batch_size: 批处理大小
            
        Returns:
            projection_output: 投影头的输出表征
        """
        assert self.net is not None, 'please train or load a net first'
        assert isinstance(data, dict) or data.ndim == 3
        if batch_size is None:
            batch_size = self.batch_size
        n_samples, ts_l, _ = data.shape if not isinstance(data, dict) else data['x'].shape

        org_training = self.net.training
        self.net.eval()
        self.adaptive_denoising.eval() # 确保门控网络在推理模式

        if isinstance(data, dict):
            data = np.concatenate((data['x'], data['mask']), axis=-1)
        dataset = TensorDataset(torch.from_numpy(data).to(torch.float))
        loader = DataLoader(dataset, batch_size=batch_size)

        with torch.no_grad():
            output = []
            for batch in loader:
                x = batch[0]
                if sliding_length is not None:
                    reprs = []
                    if n_samples < batch_size:
                        calc_buffer = []
                        calc_buffer_l = 0
                    for i in range(0, ts_l, sliding_length):
                        l = i - sliding_padding
                        r = i + sliding_length + (sliding_padding if not casual else 0)
                        x_sliding = torch_pad_nan(
                            x[:, max(l, 0): min(r, ts_l)],
                            left=-l if l < 0 else 0,
                            right=r - ts_l if r > ts_l else 0,
                            dim=1
                        )
                        if n_samples < batch_size:
                            if calc_buffer_l + n_samples > batch_size:
                                # 获取编码器输出
                                encoder_out = self._eval_with_pooling(
                                    torch.cat(calc_buffer, dim=0),
                                    mask,
                                    slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                    encoding_window=encoding_window
                                )
                                # 通过投影头
                                if encoder_out.dim() == 3:
                                    # 使用全局平均池化将时间维度压缩
                                    encoder_out = encoder_out.mean(dim=1)  # (batch, features)
                                    
                                projection_out = self.adaptive_denoising.projection_head(encoder_out.to(self.device))
                                reprs += torch.split(projection_out.cpu(), n_samples)
                                calc_buffer = []
                                calc_buffer_l = 0
                            calc_buffer.append(x_sliding)
                            calc_buffer_l += n_samples
                        else:
                            encoder_out = self._eval_with_pooling(
                                x_sliding,
                                mask,
                                slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                encoding_window=encoding_window
                            )
                            if encoder_out.dim() == 3:
                                encoder_out = encoder_out.mean(dim=1)
                            projection_out = self.adaptive_denoising.projection_head(encoder_out.to(self.device))
                            reprs.append(projection_out.cpu())

                    if n_samples < batch_size:
                        if calc_buffer_l > 0:
                            encoder_out = self._eval_with_pooling(
                                torch.cat(calc_buffer, dim=0),
                                mask,
                                slicing=slice(sliding_padding, sliding_padding + sliding_length),
                                encoding_window=encoding_window
                            )
                            if encoder_out.dim() == 3:
                                encoder_out = encoder_out.mean(dim=1)
                            projection_out = self.adaptive_denoising.projection_head(encoder_out.to(self.device))
                            reprs += torch.split(projection_out.cpu(), n_samples)
                            calc_buffer = []
                            calc_buffer_l = 0

                    out = torch.cat(reprs, dim=0)
                else:
                    encoder_out = self._eval_with_pooling(x, mask, encoding_window=encoding_window)
                    if encoding_window == 'full_series':
                        encoder_out = encoder_out.squeeze(1)
                    
                    # 处理时间维度
                    if encoder_out.dim() == 3:
                        encoder_out = encoder_out.mean(dim=1)  # 全局平均池化
                    
                    # 通过投影头
                    out = self.adaptive_denoising.projection_head(encoder_out.to(self.device)).cpu()

                output.append(out)

            output = torch.cat(output, dim=0)

        self.net.train(org_training)
        self.adaptive_denoising.train(org_training) # 确保门控网络在训练模式
        return output.numpy()

    # 移除早停性能评估方法

    def save(self, fn):
        ''' Save the model to a file.
        
        Args:
            fn (str): filename.
        '''
        torch.save(self.net.state_dict(), fn)

    def load(self, fn):
        ''' Load the model from a file.
        
        Args:
            fn (str): filename.
        '''
        state_dict = torch.load(fn, map_location=self.device)
        self.net.load_state_dict(state_dict)
