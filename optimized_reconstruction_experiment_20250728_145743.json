[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (优化版)", "success": false, "error": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\nTraceback (most recent call last):\n  File \"src/train.py\", line 181, in <module>\n    loss_log = model.fit(\n  File \"/mnt/d/WorkSpace/TimesURL/src/timesurl.py\", line 361, in fit\n    quality_scores = self.signal_quality_assessor(x_denoised_features)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1518, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1527, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/mnt/d/WorkSpace/TimesURL/src/models/signal_processing.py\", line 380, in forward\n    quality_scores = self.quality_net(signal)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1518, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1527, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/container.py\", line 215, in forward\n    input = module(input)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1518, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1527, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/torch/nn/modules/linear.py\", line 114, in forward\n    return F.linear(input, self.weight, self.bias)\nRuntimeError: mat1 and mat2 shapes cannot be multiplied (800x36 and 35x17)\n", "f1_score": 0.0, "precision": 0.0, "recall": 0.0, "training_stable": false, "loss_info": "❌ 训练失败"}]