#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车联网异常检测噪声鲁棒性可视化
运行此文件生成所有实验图表
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from matplotlib.gridspec import GridSpec
import warnings
warnings.filterwarnings('ignore')

def plot_noise_robustness_comparison():
    """绘制不同模型在噪声环境下的性能对比图"""
    # 设置中文字体和样式
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    except:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    sns.set_style("whitegrid", {'grid.alpha': 0.3})
    
    # 实验数据
    snr_levels = [30, 25, 20, 15, 10, 5]
    models_data = {
        'TranAD': [84.7, 84.1, 82.9, 80.7, 77.2, 71.8],
        'AnomalyTrans': [93.2, 92.8, 91.6, 89.8, 86.9, 81.4],
        'DCDetector': [91.3, 90.7, 89.4, 87.1, 83.8, 78.2],
        'DCRAD (Ours)': [96.9, 96.5, 95.8, 94.6, 92.7, 89.3]
    }
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 左图：性能对比曲线
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    markers = ['o', 's', '^', 'D']
    linestyles = ['--', '-.', ':', '-']
    
    for i, (model, scores) in enumerate(models_data.items()):
        if 'DCRAD' in model:
            ax1.plot(snr_levels, scores, marker=markers[i], linewidth=3.5, 
                    markersize=8, label=model, color=colors[i], 
                    linestyle=linestyles[i], markerfacecolor='white', 
                    markeredgewidth=2, markeredgecolor=colors[i])
        else:
            ax1.plot(snr_levels, scores, marker=markers[i], linewidth=2.5, 
                    markersize=6, label=model, color=colors[i], 
                    linestyle=linestyles[i], alpha=0.8)
    
    ax1.axhspan(90, 100, alpha=0.1, color='green')
    ax1.axhspan(80, 90, alpha=0.1, color='orange')
    ax1.axhspan(70, 80, alpha=0.1, color='red')
    
    ax1.set_xlabel('Signal-to-Noise Ratio (dB)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('F1-Score (%)', fontsize=12, fontweight='bold')
    ax1.set_title('Performance Comparison under Different Noise Levels', fontsize=14, fontweight='bold')
    ax1.legend(loc='lower right')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(65, 100)
    
    # 右图：性能衰减对比
    models = list(models_data.keys())
    degradation = [15.2, 12.7, 14.3, 7.8]
    
    bars = ax2.bar(models, degradation, 
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)
    
    bars[-1].set_alpha(1.0)
    bars[-1].set_edgecolor('darkgreen')
    bars[-1].set_linewidth(3)
    
    for i, (bar, value) in enumerate(zip(bars, degradation)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.3,
                f'{value}%', ha='center', va='bottom', 
                fontweight='bold' if i == 3 else 'normal',
                fontsize=11 if i == 3 else 10)
    
    ax2.set_ylabel('Performance Degradation (%)', fontsize=12, fontweight='bold')
    ax2.set_title('Performance Degradation (SNR: 30dB → 5dB)', fontsize=14, fontweight='bold')
    ax2.set_ylim(0, 18)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_xticklabels(models, rotation=15, ha='right')
    
    plt.tight_layout()
    plt.savefig('noise_robustness_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 性能对比图已保存为: noise_robustness_comparison.png")

def simulate_noise_effect_on_data():
    """模拟并可视化噪声对时间序列数据的影响"""
    np.random.seed(42)
    
    # 生成模拟数据
    time_steps = 100
    t = np.linspace(0, 10, time_steps)
    trend = 0.1 * t
    periodic = 2 * np.sin(2 * np.pi * t) + 0.5 * np.cos(4 * np.pi * t)
    anomaly_signal = np.zeros_like(t)
    anomaly_signal[60:70] = 3 * np.exp(-(t[60:70] - t[65])**2 / 0.1)
    clean_signal = trend + periodic + anomaly_signal
    
    snr_levels = [30, 20, 10, 5]
    
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    except:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    fig = plt.figure(figsize=(16, 12))
    gs = GridSpec(3, 2, height_ratios=[2, 2, 1], hspace=0.3, wspace=0.3)
    
    # 时间序列对比
    ax_signals = fig.add_subplot(gs[0, :])
    colors = ['#2E8B57', '#FF6347', '#4169E1', '#8B008B']
    
    for i, snr in enumerate(snr_levels):
        signal_power = np.mean(clean_signal**2)
        noise_power = signal_power / (10**(snr/10))
        noise = np.random.normal(0, np.sqrt(noise_power), len(clean_signal))
        noisy_signal = clean_signal + noise
        
        offset = i * 8
        ax_signals.plot(t, noisy_signal + offset, color=colors[i], 
                       linewidth=2, alpha=0.8, label=f'SNR = {snr}dB')
        
        if i == 0:
            ax_signals.axvspan(t[60], t[70], alpha=0.2, color='red', label='Anomaly Region')
    
    ax_signals.set_xlabel('Time (s)', fontsize=12, fontweight='bold')
    ax_signals.set_ylabel('Signal Amplitude', fontsize=12, fontweight='bold')
    ax_signals.set_title('BSM Signal Comparison under Different SNR Levels', fontsize=14, fontweight='bold')
    ax_signals.legend(loc='upper right')
    ax_signals.grid(True, alpha=0.3)
    
    # 频谱分析
    ax_spectrum = fig.add_subplot(gs[1, 0])
    freqs = np.fft.fftfreq(len(clean_signal), t[1] - t[0])
    freqs = freqs[:len(freqs)//2]
    
    for i, snr in enumerate([30, 5]):
        signal_power = np.mean(clean_signal**2)
        noise_power = signal_power / (10**(snr/10))
        noise = np.random.normal(0, np.sqrt(noise_power), len(clean_signal))
        noisy_signal = clean_signal + noise
        
        fft_signal = np.fft.fft(noisy_signal)
        psd = np.abs(fft_signal[:len(fft_signal)//2])**2
        
        ax_spectrum.semilogy(freqs, psd, color=colors[i*3], 
                           linewidth=2, label=f'SNR = {snr}dB')
    
    ax_spectrum.set_xlabel('Frequency (Hz)', fontsize=12, fontweight='bold')
    ax_spectrum.set_ylabel('Power Spectral Density', fontsize=12, fontweight='bold')
    ax_spectrum.set_title('Power Spectral Density Comparison', fontsize=13, fontweight='bold')
    ax_spectrum.legend()
    ax_spectrum.grid(True, alpha=0.3)
    
    # SNR vs 检测难度
    ax_difficulty = fig.add_subplot(gs[1, 1])
    snr_range = np.arange(5, 31, 1)
    detection_difficulty = 100 * np.exp(-(snr_range - 5) / 8)
    anomaly_visibility = 100 - detection_difficulty
    
    ax_difficulty.fill_between(snr_range, 0, detection_difficulty, 
                              color='red', alpha=0.3, label='Detection Difficulty')
    ax_difficulty.fill_between(snr_range, detection_difficulty, 100, 
                              color='green', alpha=0.3, label='Anomaly Visibility')
    
    ax_difficulty.plot(snr_range, detection_difficulty, 'r-', linewidth=3)
    ax_difficulty.plot(snr_range, anomaly_visibility, 'g-', linewidth=3)
    
    ax_difficulty.axvline(x=10, color='orange', linestyle='--', linewidth=2, alpha=0.8)
    ax_difficulty.text(10.5, 50, 'Critical Point\nSNR=10dB', fontsize=10, 
                      bbox=dict(boxstyle="round,pad=0.3", facecolor='orange', alpha=0.7))
    
    ax_difficulty.set_xlabel('Signal-to-Noise Ratio (dB)', fontsize=12, fontweight='bold')
    ax_difficulty.set_ylabel('Relative Metric (%)', fontsize=12, fontweight='bold')
    ax_difficulty.set_title('SNR vs Detection Difficulty', fontsize=13, fontweight='bold')
    ax_difficulty.legend()
    ax_difficulty.grid(True, alpha=0.3)
    ax_difficulty.set_ylim(0, 100)
    
    # 噪声统计特征
    ax_stats = fig.add_subplot(gs[2, :])
    snr_levels_full = [30, 25, 20, 15, 10, 5]
    noise_variance = []
    signal_to_noise = []
    
    for snr in snr_levels_full:
        signal_power = np.mean(clean_signal**2)
        noise_power = signal_power / (10**(snr/10))
        noise_variance.append(noise_power)
        signal_to_noise.append(10**(snr/10))
    
    ax_stats2 = ax_stats.twinx()
    
    bars1 = ax_stats.bar([x - 0.2 for x in range(len(snr_levels_full))], noise_variance, 
                        width=0.4, color='red', alpha=0.7, label='Noise Variance')
    bars2 = ax_stats2.bar([x + 0.2 for x in range(len(snr_levels_full))], signal_to_noise, 
                         width=0.4, color='blue', alpha=0.7, label='Signal-to-Noise Power Ratio')
    
    ax_stats.set_xlabel('Signal-to-Noise Ratio (dB)', fontsize=12, fontweight='bold')
    ax_stats.set_ylabel('Noise Variance', color='red', fontsize=12, fontweight='bold')
    ax_stats2.set_ylabel('Signal-to-Noise Power Ratio', color='blue', fontsize=12, fontweight='bold')
    ax_stats.set_title('Noise Statistical Characteristics under Different SNR Levels', fontsize=13, fontweight='bold')
    
    ax_stats.set_xticks(range(len(snr_levels_full)))
    ax_stats.set_xticklabels(snr_levels_full)
    
    lines1, labels1 = ax_stats.get_legend_handles_labels()
    lines2, labels2 = ax_stats2.get_legend_handles_labels()
    ax_stats.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    plt.suptitle('Noise Impact Analysis in V2X Anomaly Detection', fontsize=16, fontweight='bold', y=0.98)
    plt.savefig('noise_effect_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 噪声影响分析图已保存为: noise_effect_analysis.png")

def main():
    """主函数"""
    print("🚀 开始生成车联网异常检测噪声鲁棒性可视化图表...")
    print("=" * 60)
    
    try:
        print("\n📊 正在生成图表1: 模型性能对比...")
        plot_noise_robustness_comparison()
        
        print("\n📊 正在生成图表2: 噪声影响分析...")
        simulate_noise_effect_on_data()
        
        print("\n🎉 所有图表生成完成！")
        print("📁 输出文件:")
        print("   - noise_robustness_comparison.png")
        print("   - noise_effect_analysis.png")
        
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {e}")
        print("请检查依赖库是否正确安装: pip install matplotlib numpy seaborn")

if __name__ == "__main__":
    main()